package com.example.mapper;

import com.example.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单数据访问层
 */
@Mapper
public interface OrderMapper {
    
    /**
     * 查询所有订单
     */
    List<Order> selectAll();
    
    /**
     * 根据ID查询订单
     */
    Order selectById(@Param("id") Long id);
    
    /**
     * 根据订单号查询订单
     */
    Order selectByOrderNo(@Param("orderNo") String orderNo);
    
    /**
     * 插入订单
     */
    int insert(Order order);
    
    /**
     * 更新订单
     */
    int update(Order order);
    
    /**
     * 删除订单
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询订单
     */
    List<Order> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据状态查询订单
     */
    List<Order> selectByStatus(@Param("status") Integer status);
    
    /**
     * 分页查询订单
     */
    List<Order> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 统计订单总数
     */
    int countTotal();
    
    /**
     * 统计今日订单数
     */
    int countToday();
    
    /**
     * 统计今日销售额
     */
    BigDecimal sumTodayAmount();
    
    /**
     * 统计本月销售额
     */
    BigDecimal sumMonthAmount();
    
    /**
     * 更新订单状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 更新支付状态
     */
    int updatePayStatus(@Param("id") Long id, @Param("payStatus") Integer payStatus);
}
