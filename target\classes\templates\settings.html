<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 系统设置</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .setting-section {
            margin-bottom: 2rem;
        }
        .setting-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }
        .setting-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">⚙️ 系统设置</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 设置选项卡 -->
                <ul class="nav nav-tabs mb-4" id="settingsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                            <i class="fas fa-info-circle"></i> 基本设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                            <i class="fas fa-server"></i> 系统配置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                            <i class="fas fa-envelope"></i> 邮件设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                            <i class="fas fa-credit-card"></i> 支付配置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                            <i class="fas fa-shield-alt"></i> 安全设置
                        </button>
                    </li>
                </ul>

                <!-- 设置内容 -->
                <div class="tab-content" id="settingsTabContent">
                    <!-- 基本设置 -->
                    <div class="tab-pane fade show active" id="basic" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">🏪 网站基本信息</h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">网站名称</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" class="form-control" id="siteName" value="尚品甄选">
                                            <small class="text-muted">显示在浏览器标题栏和页面头部的网站名称</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">网站描述</label>
                                        </div>
                                        <div class="col-md-9">
                                            <textarea class="form-control" id="siteDescription" rows="3">尚品甄选 - 专业的电商管理平台，提供完整的商品管理、订单处理、用户管理等功能。</textarea>
                                            <small class="text-muted">网站的简短描述，用于SEO优化</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">联系邮箱</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="email" class="form-control" id="contactEmail" value="<EMAIL>">
                                            <small class="text-muted">客服联系邮箱</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">联系电话</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="tel" class="form-control" id="contactPhone" value="************">
                                            <small class="text-muted">客服联系电话</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统配置 -->
                    <div class="tab-pane fade" id="system" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">🔧 系统参数配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">分页大小</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="number" class="form-control" id="pageSize" value="20" min="10" max="100">
                                            <small class="text-muted">列表页面每页显示的记录数</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">文件上传大小限制</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="maxFileSize" value="10">
                                                <span class="input-group-text">MB</span>
                                            </div>
                                            <small class="text-muted">单个文件上传的最大大小</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">会话超时时间</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="sessionTimeout" value="30">
                                                <span class="input-group-text">分钟</span>
                                            </div>
                                            <small class="text-muted">用户会话的超时时间</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">启用调试模式</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="debugMode">
                                                <label class="form-check-label" for="debugMode">
                                                    开启后将显示详细的错误信息
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 邮件设置 -->
                    <div class="tab-pane fade" id="email" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">📧 邮件服务配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">SMTP服务器</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" class="form-control" id="smtpHost" value="smtp.qq.com">
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">SMTP端口</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="number" class="form-control" id="smtpPort" value="587">
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">发件人邮箱</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="email" class="form-control" id="senderEmail" value="<EMAIL>">
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">邮箱密码</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="password" class="form-control" id="emailPassword" placeholder="请输入邮箱密码">
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">启用SSL</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableSSL" checked>
                                                <label class="form-check-label" for="enableSSL">
                                                    使用SSL加密连接
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付配置 -->
                    <div class="tab-pane fade" id="payment" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">💳 支付方式配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">支付宝</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableAlipay" checked>
                                                <label class="form-check-label" for="enableAlipay">
                                                    启用支付宝支付
                                                </label>
                                            </div>
                                            <input type="text" class="form-control mt-2" id="alipayAppId" placeholder="支付宝应用ID">
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">微信支付</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableWechat" checked>
                                                <label class="form-check-label" for="enableWechat">
                                                    启用微信支付
                                                </label>
                                            </div>
                                            <input type="text" class="form-control mt-2" id="wechatMchId" placeholder="微信商户号">
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">货到付款</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableCOD">
                                                <label class="form-check-label" for="enableCOD">
                                                    启用货到付款
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 安全设置 -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">🔒 安全配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">密码最小长度</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="number" class="form-control" id="minPasswordLength" value="6" min="6" max="20">
                                            <small class="text-muted">用户密码的最小长度要求</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">登录失败锁定</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableLoginLock" checked>
                                                <label class="form-check-label" for="enableLoginLock">
                                                    启用登录失败锁定机制
                                                </label>
                                            </div>
                                            <input type="number" class="form-control mt-2" id="maxLoginAttempts" value="5" min="3" max="10">
                                            <small class="text-muted">最大登录失败次数</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">强制HTTPS</label>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="forceHttps">
                                                <label class="form-check-label" for="forceHttps">
                                                    强制使用HTTPS访问
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">IP白名单</label>
                                        </div>
                                        <div class="col-md-9">
                                            <textarea class="form-control" id="ipWhitelist" rows="3" placeholder="每行一个IP地址，留空表示不限制"></textarea>
                                            <small class="text-muted">只允许这些IP地址访问管理后台</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">ℹ️ 系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>系统版本:</strong></td>
                                        <td>尚品甄选 v1.0.0</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Java版本:</strong></td>
                                        <td>OpenJDK 11.0.2</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Spring Boot版本:</strong></td>
                                        <td>2.7.0</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>数据库:</strong></td>
                                        <td>H2 Database</td>
                                    </tr>
                                    <tr>
                                        <td><strong>服务器时间:</strong></td>
                                        <td id="serverTime">加载中...</td>
                                    </tr>
                                    <tr>
                                        <td><strong>运行时间:</strong></td>
                                        <td>2天 5小时 30分钟</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateServerTime();
            setInterval(updateServerTime, 1000); // 每秒更新服务器时间
        });

        // 更新服务器时间
        function updateServerTime() {
            const now = new Date();
            document.getElementById('serverTime').textContent = now.toLocaleString('zh-CN');
        }

        // 保存所有设置
        function saveAllSettings() {
            // 收集所有设置数据
            const settings = {
                basic: {
                    siteName: document.getElementById('siteName').value,
                    siteDescription: document.getElementById('siteDescription').value,
                    contactEmail: document.getElementById('contactEmail').value,
                    contactPhone: document.getElementById('contactPhone').value
                },
                system: {
                    pageSize: document.getElementById('pageSize').value,
                    maxFileSize: document.getElementById('maxFileSize').value,
                    sessionTimeout: document.getElementById('sessionTimeout').value,
                    debugMode: document.getElementById('debugMode').checked
                },
                email: {
                    smtpHost: document.getElementById('smtpHost').value,
                    smtpPort: document.getElementById('smtpPort').value,
                    senderEmail: document.getElementById('senderEmail').value,
                    emailPassword: document.getElementById('emailPassword').value,
                    enableSSL: document.getElementById('enableSSL').checked
                },
                payment: {
                    enableAlipay: document.getElementById('enableAlipay').checked,
                    alipayAppId: document.getElementById('alipayAppId').value,
                    enableWechat: document.getElementById('enableWechat').checked,
                    wechatMchId: document.getElementById('wechatMchId').value,
                    enableCOD: document.getElementById('enableCOD').checked
                },
                security: {
                    minPasswordLength: document.getElementById('minPasswordLength').value,
                    enableLoginLock: document.getElementById('enableLoginLock').checked,
                    maxLoginAttempts: document.getElementById('maxLoginAttempts').value,
                    forceHttps: document.getElementById('forceHttps').checked,
                    ipWhitelist: document.getElementById('ipWhitelist').value
                }
            };

            // 这里应该发送到后端保存
            console.log('保存设置:', settings);
            
            // 模拟保存成功
            alert('设置保存成功！');
        }

        // 重置设置
        function resetSettings() {
            if (confirm('确定要重置所有设置到默认值吗？')) {
                // 重置所有表单字段到默认值
                location.reload();
            }
        }

        // 测试邮件配置
        function testEmailConfig() {
            alert('邮件配置测试功能开发中...');
        }

        // 清除缓存
        function clearCache() {
            if (confirm('确定要清除系统缓存吗？')) {
                alert('缓存清除成功！');
            }
        }
    </script>
</body>
</html>
