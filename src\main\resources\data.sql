-- 尚品甄选电商管理系统初始化数据脚本

-- 插入初始分类数据
INSERT INTO category (name, icon, sort, status, create_time, update_time, remark) VALUES
('Electronics', '📱', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Electronic devices and accessories'),
('Fashion', '👗', 2, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Clothing and fashion accessories'),
('Home & Garden', '🏠', 3, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Home decoration and furniture'),
('Sports & Outdoors', '⚽', 4, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Sports equipment and outdoor gear'),
('Beauty & Care', '💄', 5, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Personal care and cosmetics'),
('Books & Media', '📚', 6, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Books, movies and digital media'),
('Toys & Games', '🎮', 7, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Toys and gaming products'),
('Food & Beverages', '🍎', 8, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Food and drink products');

-- 插入管理员用户
INSERT INTO user (username, password, email, real_name, role, status, create_time, update_time, remark) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', 'System Administrator', 'ADMIN', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'System administrator account');

-- 插入示例商品数据
INSERT INTO product (name, code, category_id, category_name, brand, description, current_price, original_price, stock, status, is_recommend, is_new, sort, create_time, update_time, remark) VALUES
('iPhone 15 Pro', 'P001', 1, 'Electronics', 'Apple', 'Latest iPhone with advanced features', 999.00, 1099.00, 50, 1, 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Premium smartphone'),
('Samsung Galaxy S24', 'P002', 1, 'Electronics', 'Samsung', 'Flagship Android smartphone', 899.00, 999.00, 30, 1, 1, 1, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Android flagship'),
('MacBook Air M3', 'P003', 1, 'Electronics', 'Apple', 'Lightweight laptop with M3 chip', 1299.00, 1399.00, 20, 1, 1, 0, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Professional laptop'),
('Nike Air Max', 'P004', 4, 'Sports & Outdoors', 'Nike', 'Comfortable running shoes', 129.99, 149.99, 100, 1, 0, 1, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Athletic footwear'),
('Adidas Ultraboost', 'P005', 4, 'Sports & Outdoors', 'Adidas', 'High-performance running shoes', 179.99, 199.99, 80, 1, 1, 0, 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Premium running shoes');

-- 插入示例优惠券
INSERT INTO coupon (name, code, type, value, min_amount, total_count, start_time, end_time, status, create_time, update_time, remark) VALUES
('New User Discount', 'NEW2024', 'CASH', 50.00, 100.00, 1000, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Welcome discount for new users'),
('VIP Member Discount', 'VIP2024', 'DISCOUNT', 0.15, 200.00, 500, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '15% discount for VIP members'),
('Free Shipping', 'FREESHIP', 'SHIPPING', 0.00, 50.00, 2000, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Free shipping coupon');
