// 分类管理JavaScript

let categoryModal;
let importModal;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    categoryModal = new bootstrap.Modal(document.getElementById('categoryModal'));
    importModal = new bootstrap.Modal(document.getElementById('importModal'));
    loadCategories();
});

// 加载分类列表
function loadCategories() {
    fetch('/category/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderCategoryTable(data.data);
            } else {
                showAlert('加载分类列表失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('加载分类列表失败', 'danger');
        });
}

// 渲染分类表格
function renderCategoryTable(categories) {
    const tbody = document.getElementById('categoryTableBody');
    tbody.innerHTML = '';
    
    categories.forEach(category => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-chevron-right me-2"></i>
                    ${category.name}
                </div>
            </td>
            <td>
                <div class="category-icon">
                    ${category.icon || '📦'}
                </div>
            </td>
            <td>${category.sort}</td>
            <td>
                <span class="status-badge ${category.status === 1 ? 'status-normal' : 'status-disabled'}">
                    ${category.status === 1 ? '正常' : '禁用'}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editCategory(${category.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(${category.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 显示添加分类模态框
function showAddModal() {
    document.getElementById('categoryModalTitle').textContent = '添加分类';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    categoryModal.show();
}

// 编辑分类
function editCategory(id) {
    fetch(`/category/list`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const category = data.data.find(c => c.id === id);
                if (category) {
                    document.getElementById('categoryModalTitle').textContent = '编辑分类';
                    document.getElementById('categoryId').value = category.id;
                    document.getElementById('categoryName').value = category.name;
                    document.getElementById('categoryIcon').value = category.icon || '';
                    document.getElementById('categorySort').value = category.sort;
                    document.getElementById('categoryStatus').value = category.status;
                    document.getElementById('categoryRemark').value = category.remark || '';
                    categoryModal.show();
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('获取分类信息失败', 'danger');
        });
}

// 保存分类
function saveCategory() {
    const id = document.getElementById('categoryId').value;
    const category = {
        name: document.getElementById('categoryName').value,
        icon: document.getElementById('categoryIcon').value,
        sort: parseInt(document.getElementById('categorySort').value),
        status: parseInt(document.getElementById('categoryStatus').value),
        remark: document.getElementById('categoryRemark').value
    };
    
    if (id) {
        category.id = parseInt(id);
    }
    
    const url = id ? '/category/update' : '/category/add';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(category)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            categoryModal.hide();
            loadCategories();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('保存失败', 'danger');
    });
}

// 删除分类
function deleteCategory(id) {
    if (confirm('确定要删除这个分类吗？')) {
        fetch(`/category/delete/${id}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadCategories();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('删除失败', 'danger');
        });
    }
}

// 导出数据
function exportData() {
    window.location.href = '/category/export';
}

// 显示导入模态框
function showImportModal() {
    document.getElementById('importForm').reset();
    importModal.show();
}

// 导入数据
function importData() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];
    
    if (!file) {
        showAlert('请选择要导入的文件', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    fetch('/category/import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            importModal.hide();
            loadCategories();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('导入失败', 'danger');
    });
}

// 显示提示信息
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.content-card');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
