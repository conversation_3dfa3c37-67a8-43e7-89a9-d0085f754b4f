<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选后台管理系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .brand-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
        }
        
        .brand-title h2 {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .brand-subtitle {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 0;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 1rem 1rem 3rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
            font-size: 1.1rem;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .captcha-container {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .captcha-image {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.2rem;
            color: #495057;
            letter-spacing: 3px;
            user-select: none;
        }
        
        .captcha-image:hover {
            border-color: #667eea;
            background: white;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: #764ba2;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <!-- 浮动装饰元素 -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="brand-title">
            <h2>🛍️ 尚品甄选后台管理系统</h2>
            <p class="brand-subtitle">Premium E-commerce Management</p>
        </div>

        <!-- 错误提示 -->
        <div id="errorAlert" class="alert alert-danger" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorMessage"></span>
        </div>

        <!-- 登录表单 -->
        <form id="loginForm">
            <!-- 用户名输入 -->
            <div class="input-group">
                <i class="fas fa-user input-icon"></i>
                <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
            </div>

            <!-- 密码输入 -->
            <div class="input-group">
                <i class="fas fa-lock input-icon"></i>
                <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                <i class="fas fa-eye position-absolute end-0 top-50 translate-middle-y me-3" 
                   id="togglePassword" style="cursor: pointer; z-index: 10;"></i>
            </div>

            <!-- 验证码输入 -->
            <div class="captcha-container">
                <div class="input-group flex-grow-1">
                    <i class="fas fa-shield-alt input-icon"></i>
                    <input type="text" class="form-control" id="captcha" name="captcha" placeholder="请输入验证码" required>
                </div>
                <div class="captcha-image" id="captchaImage" onclick="refreshCaptcha()">
                    <span id="captchaText">1SC2</span>
                </div>
            </div>

            <!-- 记住我 -->
            <div class="remember-me">
                <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    记住我
                </label>
            </div>

            <!-- 登录按钮 -->
            <button type="submit" class="btn btn-login">
                <span class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                <span class="login-text">登录</span>
            </button>
        </form>

        <!-- 忘记密码 -->
        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">忘记密码？</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 验证码字符集
        const captchaChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let currentCaptcha = '';

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            refreshCaptcha();
            
            // 检查是否有记住的用户名
            const rememberedUsername = localStorage.getItem('rememberedUsername');
            if (rememberedUsername) {
                document.getElementById('username').value = rememberedUsername;
                document.getElementById('rememberMe').checked = true;
            }
        });

        // 生成验证码
        function generateCaptcha() {
            let captcha = '';
            for (let i = 0; i < 4; i++) {
                captcha += captchaChars.charAt(Math.floor(Math.random() * captchaChars.length));
            }
            return captcha;
        }

        // 刷新验证码
        function refreshCaptcha() {
            const captchaText = document.getElementById('captchaText');
            const captchaImage = document.getElementById('captchaImage');

            // 显示加载状态
            captchaText.textContent = '...';
            captchaImage.style.opacity = '0.6';
            captchaImage.style.cursor = 'wait';

            // 从后端获取新的验证码
            fetch('/captcha')
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200 && result.data) {
                        // 验证码在data字段中
                        currentCaptcha = result.data;
                        captchaText.textContent = currentCaptcha;
                        console.log('验证码已刷新:', currentCaptcha);
                    } else {
                        // 如果后端获取失败，使用前端生成
                        currentCaptcha = generateCaptcha();
                        captchaText.textContent = currentCaptcha;
                        console.log('使用前端生成验证码:', currentCaptcha);
                    }
                })
                .catch(error => {
                    console.error('Captcha error:', error);
                    // 如果网络错误，使用前端生成
                    currentCaptcha = generateCaptcha();
                    captchaText.textContent = currentCaptcha;
                    console.log('网络错误，使用前端生成验证码:', currentCaptcha);
                })
                .finally(() => {
                    // 恢复正常状态
                    captchaImage.style.opacity = '1';
                    captchaImage.style.cursor = 'pointer';
                });
        }

        // 切换密码显示/隐藏
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this;
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // 基本验证
            if (!username || !password || !captcha) {
                showError('请填写完整的登录信息');
                return;
            }

            // 显示加载状态
            showLoading(true);

            // 发送登录请求到后端
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            formData.append('captcha', captcha);

            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 200) {
                    // 记住用户名
                    if (rememberMe) {
                        localStorage.setItem('rememberedUsername', username);
                    } else {
                        localStorage.removeItem('rememberedUsername');
                    }

                    // 登录成功，跳转到仪表盘
                    window.location.href = '/dashboard';
                } else {
                    showError(result.message || '登录失败');
                    refreshCaptcha();
                    document.getElementById('captcha').value = '';
                    showLoading(false);
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                showError('网络错误，请稍后重试');
                refreshCaptcha();
                document.getElementById('captcha').value = '';
                showLoading(false);
            });
        });

        // 显示错误信息
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            
            errorMessage.textContent = message;
            errorAlert.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                errorAlert.style.display = 'none';
            }, 3000);
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.querySelector('.loading');
            const loginText = document.querySelector('.login-text');
            const submitBtn = document.querySelector('.btn-login');
            
            if (show) {
                loading.classList.add('show');
                loginText.textContent = '登录中...';
                submitBtn.disabled = true;
            } else {
                loading.classList.remove('show');
                loginText.textContent = '登录';
                submitBtn.disabled = false;
            }
        }

        // 忘记密码
        function showForgotPassword() {
            alert('请联系系统管理员重置密码\n邮箱: <EMAIL>\n电话: ************');
        }

        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
