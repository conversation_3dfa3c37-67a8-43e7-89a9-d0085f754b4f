package com.example.mapper;

import com.example.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 分类数据访问层
 */
@Mapper
public interface CategoryMapper {
    
    /**
     * 查询所有分类
     */
    List<Category> selectAll();
    
    /**
     * 根据ID查询分类
     */
    Category selectById(@Param("id") Long id);
    
    /**
     * 插入分类
     */
    int insert(Category category);
    
    /**
     * 更新分类
     */
    int update(Category category);
    
    /**
     * 删除分类
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据状态查询分类
     */
    List<Category> selectByStatus(@Param("status") Integer status);
    
    /**
     * 批量插入分类
     */
    int batchInsert(@Param("categories") List<Category> categories);
}
