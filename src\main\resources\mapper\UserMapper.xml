<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="com.example.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="realName" column="real_name"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="role" column="role"/>
        <result property="status" column="status"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 查询所有用户 -->
    <select id="selectAll" resultMap="UserResultMap">
        SELECT * FROM user ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询用户 -->
    <select id="selectById" resultMap="UserResultMap">
        SELECT * FROM user WHERE id = #{id}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="UserResultMap">
        SELECT * FROM user WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="UserResultMap">
        SELECT * FROM user WHERE email = #{email}
    </select>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.example.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (username, password, email, phone, real_name, avatar, gender, birthday, role, status, create_time, update_time, remark)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{realName}, #{avatar}, #{gender}, #{birthday}, #{role}, #{status}, #{createTime}, #{updateTime}, #{remark})
    </insert>

    <!-- 更新用户 -->
    <update id="update" parameterType="com.example.entity.User">
        UPDATE user SET
            username = #{username},
            password = #{password},
            email = #{email},
            phone = #{phone},
            real_name = #{realName},
            avatar = #{avatar},
            gender = #{gender},
            birthday = #{birthday},
            role = #{role},
            status = #{status},
            update_time = #{updateTime},
            remark = #{remark}
        WHERE id = #{id}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteById">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 根据角色查询用户 -->
    <select id="selectByRole" resultMap="UserResultMap">
        SELECT * FROM user WHERE role = #{role} ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询用户 -->
    <select id="selectByStatus" resultMap="UserResultMap">
        SELECT * FROM user WHERE status = #{status} ORDER BY create_time DESC
    </select>

    <!-- 分页查询用户 -->
    <select id="selectByPage" resultMap="UserResultMap">
        SELECT * FROM user ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计用户总数 -->
    <select id="countTotal" resultType="int">
        SELECT COUNT(*) FROM user
    </select>

    <!-- 更新最后登录信息 -->
    <update id="updateLastLogin">
        UPDATE user SET
            last_login_time = #{loginTime},
            last_login_ip = #{loginIp}
        WHERE id = #{id}
    </update>

</mapper>
