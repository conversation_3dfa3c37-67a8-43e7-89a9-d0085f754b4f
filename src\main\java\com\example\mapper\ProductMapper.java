package com.example.mapper;

import com.example.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 商品数据访问层
 */
@Mapper
public interface ProductMapper {
    
    /**
     * 查询所有商品
     */
    List<Product> selectAll();
    
    /**
     * 根据ID查询商品
     */
    Product selectById(@Param("id") Long id);
    
    /**
     * 根据商品编码查询商品
     */
    Product selectByCode(@Param("code") String code);
    
    /**
     * 插入商品
     */
    int insert(Product product);
    
    /**
     * 更新商品
     */
    int update(Product product);
    
    /**
     * 删除商品
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据分类查询商品
     */
    List<Product> selectByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 根据状态查询商品
     */
    List<Product> selectByStatus(@Param("status") Integer status);
    
    /**
     * 查询推荐商品
     */
    List<Product> selectRecommended();
    
    /**
     * 查询新品
     */
    List<Product> selectNew();
    
    /**
     * 查询热销商品
     */
    List<Product> selectHot();
    
    /**
     * 分页查询商品
     */
    List<Product> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 根据关键词搜索商品
     */
    List<Product> searchByKeyword(@Param("keyword") String keyword);
    
    /**
     * 统计商品总数
     */
    int countTotal();
    
    /**
     * 统计库存不足商品数量
     */
    int countLowStock();
    
    /**
     * 更新库存
     */
    int updateStock(@Param("id") Long id, @Param("stock") Integer stock);
    
    /**
     * 增加销量
     */
    int increaseSales(@Param("id") Long id, @Param("quantity") Integer quantity);
}
