package com.example.controller;

import com.example.entity.Product;
import com.example.service.ProductService;
import com.example.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品控制器
 */
@RestController
@RequestMapping("/product")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    /**
     * 获取所有商品
     */
    @GetMapping("/list")
    public Result<List<Product>> getAllProducts() {
        return productService.getAllProducts();
    }
    
    /**
     * 根据ID获取商品
     */
    @GetMapping("/{id}")
    public Result<Product> getProductById(@PathVariable Long id) {
        return productService.getProductById(id);
    }
    
    /**
     * 添加商品
     */
    @PostMapping("/add")
    public Result<String> addProduct(@Valid @RequestBody Product product) {
        return productService.addProduct(product);
    }
    
    /**
     * 更新商品
     */
    @PutMapping("/update")
    public Result<String> updateProduct(@Valid @RequestBody Product product) {
        return productService.updateProduct(product);
    }
    
    /**
     * 删除商品
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteProduct(@PathVariable Long id) {
        return productService.deleteProduct(id);
    }
    
    /**
     * 根据分类获取商品
     */
    @GetMapping("/category/{categoryId}")
    public Result<List<Product>> getProductsByCategory(@PathVariable Long categoryId) {
        return productService.getProductsByCategory(categoryId);
    }
    
    /**
     * 搜索商品
     */
    @GetMapping("/search")
    public Result<List<Product>> searchProducts(@RequestParam String keyword) {
        return productService.searchProducts(keyword);
    }
    
    /**
     * 获取推荐商品
     */
    @GetMapping("/recommended")
    public Result<List<Product>> getRecommendedProducts() {
        return productService.getRecommendedProducts();
    }
    
    /**
     * 获取新品
     */
    @GetMapping("/new")
    public Result<List<Product>> getNewProducts() {
        return productService.getNewProducts();
    }
    
    /**
     * 获取热销商品
     */
    @GetMapping("/hot")
    public Result<List<Product>> getHotProducts() {
        return productService.getHotProducts();
    }
    
    /**
     * 更新库存
     */
    @PutMapping("/{id}/stock")
    public Result<String> updateStock(@PathVariable Long id, @RequestParam Integer stock) {
        return productService.updateStock(id, stock);
    }
}
