<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.CategoryMapper">

    <!-- 结果映射 -->
    <resultMap id="CategoryResultMap" type="com.example.entity.Category">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 查询所有分类 -->
    <select id="selectAll" resultMap="CategoryResultMap">
        SELECT id, name, icon, sort, status, create_time, update_time, remark
        FROM category
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据ID查询分类 -->
    <select id="selectById" resultMap="CategoryResultMap">
        SELECT id, name, icon, sort, status, create_time, update_time, remark
        FROM category
        WHERE id = #{id}
    </select>

    <!-- 插入分类 -->
    <insert id="insert" parameterType="com.example.entity.Category" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO category (name, icon, sort, status, create_time, update_time, remark)
        VALUES (#{name}, #{icon}, #{sort}, #{status}, #{createTime}, #{updateTime}, #{remark})
    </insert>

    <!-- 更新分类 -->
    <update id="update" parameterType="com.example.entity.Category">
        UPDATE category
        SET name = #{name},
            icon = #{icon},
            sort = #{sort},
            status = #{status},
            update_time = #{updateTime},
            remark = #{remark}
        WHERE id = #{id}
    </update>

    <!-- 删除分类 -->
    <delete id="deleteById">
        DELETE FROM category WHERE id = #{id}
    </delete>

    <!-- 根据状态查询分类 -->
    <select id="selectByStatus" resultMap="CategoryResultMap">
        SELECT id, name, icon, sort, status, create_time, update_time, remark
        FROM category
        WHERE status = #{status}
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 批量插入分类 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO category (name, icon, sort, status, create_time, update_time, remark)
        VALUES
        <foreach collection="categories" item="category" separator=",">
            (#{category.name}, #{category.icon}, #{category.sort}, #{category.status}, 
             #{category.createTime}, #{category.updateTime}, #{category.remark})
        </foreach>
    </insert>

</mapper>
