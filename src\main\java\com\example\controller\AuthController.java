package com.example.controller;

import com.example.common.Result;
import com.example.entity.User;
import com.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;

/**
 * 认证控制器
 */
@Controller
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model) {
        return "login";
    }
    
    /**
     * 首页重定向到登录页
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }
    
    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    @ResponseBody
    public Result<String> login(@RequestParam String username, 
                               @RequestParam String password,
                               @RequestParam String captcha,
                               HttpSession session) {
        try {
            // 验证验证码
            String sessionCaptcha = (String) session.getAttribute("captcha");
            if (sessionCaptcha == null || !sessionCaptcha.equalsIgnoreCase(captcha)) {
                return Result.error("验证码错误");
            }
            
            // 验证用户名和密码
            Result<User> loginResult = userService.login(username, password);
            if (loginResult.getCode() == 200) {
                // 登录成功，将用户信息存入session
                session.setAttribute("currentUser", loginResult.getData());
                session.removeAttribute("captcha"); // 清除验证码
                return Result.success("登录成功");
            } else {
                return Result.error(loginResult.getMessage());
            }
        } catch (Exception e) {
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/login";
    }
    
    /**
     * 生成验证码
     */
    @GetMapping("/captcha")
    @ResponseBody
    public java.util.Map<String, Object> generateCaptcha(HttpSession session) {
        // 生成4位随机验证码
        String captcha = generateRandomCaptcha();
        session.setAttribute("captcha", captcha);

        // 直接返回Map
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("code", 200);
        result.put("message", "验证码生成成功");
        result.put("data", captcha);
        result.put("success", true);
        result.put("error", false);

        return result;
    }
    
    /**
     * 检查登录状态
     */
    @GetMapping("/check-login")
    @ResponseBody
    public Result<User> checkLogin(HttpSession session) {
        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser != null) {
            return Result.success(currentUser);
        } else {
            return Result.error("未登录");
        }
    }
    
    /**
     * 生成随机验证码
     */
    private String generateRandomCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return captcha.toString();
    }
}
