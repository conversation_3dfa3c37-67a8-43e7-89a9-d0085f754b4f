package com.example.controller;

import com.example.entity.Category;
import com.example.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分类管理控制器
 */
@Slf4j
@Controller
@RequestMapping("/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 分类管理页面
     */
    @GetMapping("/index")
    public String index(Model model) {
        List<Category> categories = categoryService.getAllCategories();
        model.addAttribute("categories", categories);
        return "category/index";
    }

    /**
     * 获取所有分类数据
     */
    @GetMapping("/list")
    @ResponseBody
    public Map<String, Object> list() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Category> categories = categoryService.getAllCategories();
            result.put("success", true);
            result.put("data", categories);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            result.put("success", false);
            result.put("message", "获取分类列表失败");
        }
        return result;
    }

    /**
     * 添加分类
     */
    @PostMapping("/add")
    @ResponseBody
    public Map<String, Object> add(@RequestBody Category category) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = categoryService.addCategory(category);
            result.put("success", success);
            result.put("message", success ? "添加成功" : "添加失败");
        } catch (Exception e) {
            log.error("添加分类失败", e);
            result.put("success", false);
            result.put("message", "添加分类失败");
        }
        return result;
    }

    /**
     * 更新分类
     */
    @PostMapping("/update")
    @ResponseBody
    public Map<String, Object> update(@RequestBody Category category) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = categoryService.updateCategory(category);
            result.put("success", success);
            result.put("message", success ? "更新成功" : "更新失败");
        } catch (Exception e) {
            log.error("更新分类失败", e);
            result.put("success", false);
            result.put("message", "更新分类失败");
        }
        return result;
    }

    /**
     * 删除分类
     */
    @PostMapping("/delete/{id}")
    @ResponseBody
    public Map<String, Object> delete(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = categoryService.deleteCategory(id);
            result.put("success", success);
            result.put("message", success ? "删除成功" : "删除失败");
        } catch (Exception e) {
            log.error("删除分类失败", e);
            result.put("success", false);
            result.put("message", "删除分类失败");
        }
        return result;
    }

    /**
     * 导出分类数据
     */
    @GetMapping("/export")
    public void export(HttpServletResponse response) {
        categoryService.exportCategories(response);
    }

    /**
     * 导入分类数据
     */
    @PostMapping("/import")
    @ResponseBody
    public Map<String, Object> importCategories(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = categoryService.importCategories(file);
            result.put("success", success);
            result.put("message", success ? "导入成功" : "导入失败");
        } catch (Exception e) {
            log.error("导入分类数据失败", e);
            result.put("success", false);
            result.put("message", "导入分类数据失败");
        }
        return result;
    }
}
