package com.example.controller;

import com.example.service.StatisticsService;
import com.example.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 统计分析控制器
 */
@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {
    
    @Autowired
    private StatisticsService statisticsService;
    
    /**
     * 获取系统概览统计数据
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getOverviewStatistics() {
        return statisticsService.getOverviewStatistics();
    }
    
    /**
     * 获取销售趋势数据
     */
    @GetMapping("/sales-trend")
    public Result<Map<String, Object>> getSalesTrend() {
        return statisticsService.getSalesTrend();
    }
    
    /**
     * 获取商品分类销售统计
     */
    @GetMapping("/category-sales")
    public Result<Map<String, Object>> getCategorySalesStatistics() {
        return statisticsService.getCategorySalesStatistics();
    }
    
    /**
     * 获取用户增长统计
     */
    @GetMapping("/user-growth")
    public Result<Map<String, Object>> getUserGrowthStatistics() {
        return statisticsService.getUserGrowthStatistics();
    }
}
