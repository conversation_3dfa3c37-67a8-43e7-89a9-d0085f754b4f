<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 订单管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status-badge {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🛒 订单管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 新建订单
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportOrders()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索订单号或用户">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">待付款</option>
                                    <option value="2">待发货</option>
                                    <option value="3">已发货</option>
                                    <option value="4">已完成</option>
                                    <option value="5">已取消</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="payStatusFilter">
                                    <option value="">支付状态</option>
                                    <option value="0">未支付</option>
                                    <option value="1">已支付</option>
                                    <option value="2">已退款</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary" onclick="searchOrders()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">订单列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>用户信息</th>
                                        <th>商品信息</th>
                                        <th>订单金额</th>
                                        <th>订单状态</th>
                                        <th>支付状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="orderTableBody">
                                    <!-- 订单数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="订单分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">订单详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderDetailContent">
                    <!-- 订单详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
        });

        // 加载订单列表
        function loadOrders() {
            fetch('/order/list')
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        displayOrders(result.data);
                    } else {
                        alert('加载订单列表失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 显示模拟数据
                    displayOrders(getMockOrders());
                });
        }

        // 显示订单列表
        function displayOrders(orders) {
            const tbody = document.getElementById('orderTableBody');
            tbody.innerHTML = '';

            orders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <strong>${order.orderNo}</strong>
                    </td>
                    <td>
                        <div>
                            <strong>${order.userName || '用户' + order.userId}</strong><br>
                            <small class="text-muted">${order.userPhone || '未设置'}</small>
                        </div>
                    </td>
                    <td>
                        <small>${order.productCount || 1} 件商品</small>
                    </td>
                    <td>
                        <strong class="text-danger">¥${order.totalAmount}</strong>
                    </td>
                    <td>
                        <span class="badge ${getOrderStatusBadgeClass(order.status)} status-badge">
                            ${getOrderStatusText(order.status)}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${getPayStatusBadgeClass(order.payStatus)} status-badge">
                            ${getPayStatusText(order.payStatus)}
                        </span>
                    </td>
                    <td>
                        <small>${formatDateTime(order.createTime)}</small>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-info" onclick="viewOrderDetail(${order.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="editOrder(${order.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteOrder(${order.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取订单状态徽章样式
        function getOrderStatusBadgeClass(status) {
            switch(status) {
                case 1: return 'bg-warning';
                case 2: return 'bg-info';
                case 3: return 'bg-primary';
                case 4: return 'bg-success';
                case 5: return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        // 获取订单状态文本
        function getOrderStatusText(status) {
            switch(status) {
                case 1: return '待付款';
                case 2: return '待发货';
                case 3: return '已发货';
                case 4: return '已完成';
                case 5: return '已取消';
                default: return '未知';
            }
        }

        // 获取支付状态徽章样式
        function getPayStatusBadgeClass(status) {
            switch(status) {
                case 0: return 'bg-warning';
                case 1: return 'bg-success';
                case 2: return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        // 获取支付状态文本
        function getPayStatusText(status) {
            switch(status) {
                case 0: return '未支付';
                case 1: return '已支付';
                case 2: return '已退款';
                default: return '未知';
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 查看订单详情
        function viewOrderDetail(id) {
            // 这里应该从API获取订单详情
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>订单信息</h6>
                        <p><strong>订单号：</strong>ORD${id}202406010001</p>
                        <p><strong>下单时间：</strong>2024-06-01 10:30:00</p>
                        <p><strong>订单状态：</strong><span class="badge bg-success">已完成</span></p>
                    </div>
                    <div class="col-md-6">
                        <h6>收货信息</h6>
                        <p><strong>收货人：</strong>张三</p>
                        <p><strong>联系电话：</strong>138****8888</p>
                        <p><strong>收货地址：</strong>北京市朝阳区xxx街道xxx号</p>
                    </div>
                </div>
                <hr>
                <h6>商品信息</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>iPhone 15 Pro</td>
                                <td>¥999.00</td>
                                <td>1</td>
                                <td>¥999.00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-end">
                    <h5>订单总额：<span class="text-danger">¥999.00</span></h5>
                </div>
            `;
            
            document.getElementById('orderDetailContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('orderDetailModal')).show();
        }

        // 编辑订单
        function editOrder(id) {
            alert('编辑订单功能开发中...');
        }

        // 删除订单
        function deleteOrder(id) {
            if (confirm('确定要删除这个订单吗？')) {
                fetch(`/order/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        alert(result.message);
                        loadOrders();
                    } else {
                        alert('删除失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }

        // 搜索订单
        function searchOrders() {
            alert('搜索功能开发中...');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('payStatusFilter').value = '';
            loadOrders();
        }

        // 显示添加模态框
        function showAddModal() {
            alert('新建订单功能开发中...');
        }

        // 导出订单
        function exportOrders() {
            alert('导出功能开发中...');
        }

        // 获取模拟订单数据
        function getMockOrders() {
            return [
                {
                    id: 1,
                    orderNo: 'ORD202406010001',
                    userId: 1,
                    userName: '张三',
                    userPhone: '138****8888',
                    totalAmount: 999.00,
                    status: 4,
                    payStatus: 1,
                    createTime: '2024-06-01T10:30:00',
                    productCount: 1
                },
                {
                    id: 2,
                    orderNo: 'ORD202406010002',
                    userId: 2,
                    userName: '李四',
                    userPhone: '139****9999',
                    totalAmount: 1299.00,
                    status: 3,
                    payStatus: 1,
                    createTime: '2024-06-01T11:15:00',
                    productCount: 1
                },
                {
                    id: 3,
                    orderNo: 'ORD202406010003',
                    userId: 3,
                    userName: '王五',
                    userPhone: '137****7777',
                    totalAmount: 129.99,
                    status: 1,
                    payStatus: 0,
                    createTime: '2024-06-01T14:20:00',
                    productCount: 1
                }
            ];
        }
    </script>
</body>
</html>
