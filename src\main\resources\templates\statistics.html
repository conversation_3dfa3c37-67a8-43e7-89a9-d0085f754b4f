<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 统计分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card-warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stat-card-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        }
        .chart-container {
            position: relative;
            height: 400px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📊 统计分析</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                                <i class="fas fa-refresh"></i> 刷新数据
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-download"></i> 导出报表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 核心指标卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">总用户数</div>
                                        <div class="h5 mb-0 font-weight-bold" id="totalUsers">加载中...</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card-success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">商品总数</div>
                                        <div class="h5 mb-0 font-weight-bold" id="totalProducts">加载中...</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-box fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card-warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">今日订单</div>
                                        <div class="h5 mb-0 font-weight-bold" id="todayOrders">加载中...</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card-danger">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">今日销售额</div>
                                        <div class="h5 mb-0 font-weight-bold" id="todaySales">加载中...</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">📈 销售趋势</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">🏷️ 分类销售占比</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="categoryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户增长和商品统计 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">👥 用户增长趋势</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="userGrowthChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">📦 库存预警</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning" role="alert">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span id="lowStockAlert">正在检查库存状态...</span>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>商品名称</th>
                                                <th>当前库存</th>
                                                <th>预警值</th>
                                            </tr>
                                        </thead>
                                        <tbody id="lowStockTable">
                                            <!-- 低库存商品数据 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">📋 详细数据</h5>
                            </div>
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="dataTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab">
                                            销售数据
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">
                                            商品数据
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                                            用户数据
                                        </button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="dataTabContent">
                                    <div class="tab-pane fade show active" id="sales" role="tabpanel">
                                        <div class="table-responsive mt-3">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>日期</th>
                                                        <th>订单数</th>
                                                        <th>销售额</th>
                                                        <th>平均客单价</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="salesDataTable">
                                                    <!-- 销售数据 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="products" role="tabpanel">
                                        <div class="table-responsive mt-3">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>商品名称</th>
                                                        <th>分类</th>
                                                        <th>销量</th>
                                                        <th>库存</th>
                                                        <th>销售额</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="productsDataTable">
                                                    <!-- 商品数据 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="users" role="tabpanel">
                                        <div class="table-responsive mt-3">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>用户类型</th>
                                                        <th>用户数量</th>
                                                        <th>占比</th>
                                                        <th>活跃度</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="usersDataTable">
                                                    <!-- 用户数据 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 图表实例
        let salesChart, categoryChart, userGrowthChart;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            initCharts();
        });

        // 加载统计数据
        function loadStatistics() {
            // 模拟数据，实际应该从API获取
            document.getElementById('totalUsers').textContent = '1,234';
            document.getElementById('totalProducts').textContent = '567';
            document.getElementById('todayOrders').textContent = '89';
            document.getElementById('todaySales').textContent = '¥12,345';
            
            // 库存预警
            document.getElementById('lowStockAlert').textContent = '发现 3 个商品库存不足，需要及时补货！';
            
            // 加载低库存商品
            loadLowStockProducts();
            
            // 加载详细数据表格
            loadDetailedData();
        }

        // 初始化图表
        function initCharts() {
            // 销售趋势图
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            salesChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售额 (¥)',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 分类销售占比图
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Electronics', 'Fashion', 'Home & Garden', 'Sports', 'Beauty'],
                    datasets: [{
                        data: [35, 25, 20, 12, 8],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });

            // 用户增长趋势图
            const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
            userGrowthChart = new Chart(userGrowthCtx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '新增用户',
                        data: [120, 150, 180, 200, 230, 250],
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 加载低库存商品
        function loadLowStockProducts() {
            const tbody = document.getElementById('lowStockTable');
            const lowStockData = [
                { name: 'iPhone 15 Pro', stock: 5, warning: 10 },
                { name: 'Samsung Galaxy S24', stock: 3, warning: 10 },
                { name: 'MacBook Air M3', stock: 2, warning: 5 }
            ];

            tbody.innerHTML = '';
            lowStockData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name}</td>
                    <td class="text-danger">${item.stock}</td>
                    <td>${item.warning}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 加载详细数据
        function loadDetailedData() {
            // 销售数据
            const salesData = [
                { date: '2024-06-01', orders: 25, sales: 12500, avgOrder: 500 },
                { date: '2024-05-31', orders: 30, sales: 15000, avgOrder: 500 },
                { date: '2024-05-30', orders: 22, sales: 11000, avgOrder: 500 }
            ];

            const salesTableBody = document.getElementById('salesDataTable');
            salesTableBody.innerHTML = '';
            salesData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.date}</td>
                    <td>${item.orders}</td>
                    <td>¥${item.sales.toLocaleString()}</td>
                    <td>¥${item.avgOrder}</td>
                `;
                salesTableBody.appendChild(row);
            });

            // 商品数据
            const productsData = [
                { name: 'iPhone 15 Pro', category: 'Electronics', sales: 150, stock: 50, revenue: 149850 },
                { name: 'Samsung Galaxy S24', category: 'Electronics', sales: 120, stock: 30, revenue: 107880 },
                { name: 'MacBook Air M3', category: 'Electronics', sales: 80, stock: 20, revenue: 103920 }
            ];

            const productsTableBody = document.getElementById('productsDataTable');
            productsTableBody.innerHTML = '';
            productsData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name}</td>
                    <td>${item.category}</td>
                    <td>${item.sales}</td>
                    <td>${item.stock}</td>
                    <td>¥${item.revenue.toLocaleString()}</td>
                `;
                productsTableBody.appendChild(row);
            });

            // 用户数据
            const usersData = [
                { type: '管理员', count: 5, percentage: '0.4%', activity: '高' },
                { type: 'VIP用户', count: 123, percentage: '10.0%', activity: '高' },
                { type: '普通用户', count: 1106, percentage: '89.6%', activity: '中' }
            ];

            const usersTableBody = document.getElementById('usersDataTable');
            usersTableBody.innerHTML = '';
            usersData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.type}</td>
                    <td>${item.count}</td>
                    <td>${item.percentage}</td>
                    <td>
                        <span class="badge ${item.activity === '高' ? 'bg-success' : 'bg-warning'}">${item.activity}</span>
                    </td>
                `;
                usersTableBody.appendChild(row);
            });
        }

        // 刷新数据
        function refreshData() {
            loadStatistics();
            // 更新图表数据
            if (salesChart) {
                salesChart.update();
            }
            if (categoryChart) {
                categoryChart.update();
            }
            if (userGrowthChart) {
                userGrowthChart.update();
            }
            alert('数据已刷新！');
        }

        // 导出报表
        function exportReport() {
            alert('报表导出功能开发中...');
        }
    </script>
</body>
</html>
