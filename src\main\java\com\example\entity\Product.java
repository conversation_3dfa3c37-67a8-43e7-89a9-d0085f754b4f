package com.example.entity;

import lombok.Data;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品实体类
 */
@Data
public class Product {
    
    /**
     * 商品ID
     */
    private Long id;
    
    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String name;
    
    /**
     * 商品编码
     */
    private String code;
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类不能为空")
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 商品详情
     */
    private String detail;
    
    /**
     * 主图
     */
    private String mainImage;
    
    /**
     * 图片列表(JSON格式)
     */
    private String images;
    
    /**
     * 原价
     */
    @DecimalMin(value = "0.01", message = "原价必须大于0")
    private BigDecimal originalPrice;
    
    /**
     * 现价
     */
    @DecimalMin(value = "0.01", message = "现价必须大于0")
    private BigDecimal currentPrice;
    
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    
    /**
     * 库存数量
     */
    private Integer stock;
    
    /**
     * 预警库存
     */
    private Integer warningStock;
    
    /**
     * 销量
     */
    private Integer sales;
    
    /**
     * 重量(克)
     */
    private Integer weight;
    
    /**
     * 规格参数(JSON格式)
     */
    private String specifications;
    
    /**
     * 商品标签
     */
    private String tags;
    
    /**
     * 是否推荐 (0-否, 1-是)
     */
    private Integer isRecommend;
    
    /**
     * 是否新品 (0-否, 1-是)
     */
    private Integer isNew;
    
    /**
     * 是否热销 (0-否, 1-是)
     */
    private Integer isHot;
    
    /**
     * 状态 (0-下架, 1-上架, 2-草稿)
     */
    private Integer status;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
