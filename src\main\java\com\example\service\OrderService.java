package com.example.service;

import com.example.common.Result;
import com.example.entity.Order;
import com.example.mapper.OrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 订单服务层
 */
@Service
public class OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    /**
     * 查询所有订单
     */
    public Result<List<Order>> getAllOrders() {
        try {
            List<Order> orders = orderMapper.selectAll();
            return Result.success(orders);
        } catch (Exception e) {
            return Result.error("查询订单列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询订单
     */
    public Result<Order> getOrderById(Long id) {
        try {
            Order order = orderMapper.selectById(id);
            if (order != null) {
                return Result.success(order);
            }
            return Result.error("订单不存在");
        } catch (Exception e) {
            return Result.error("查询订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加订单
     */
    public Result<String> addOrder(Order order) {
        try {
            // 生成订单号
            order.setOrderNo(generateOrderNo());
            
            // 设置创建时间
            order.setCreateTime(LocalDateTime.now());
            order.setUpdateTime(LocalDateTime.now());
            
            // 设置默认状态
            if (order.getStatus() == null) {
                order.setStatus(1); // 待付款
            }
            if (order.getPayStatus() == null) {
                order.setPayStatus(0); // 未支付
            }
            
            int result = orderMapper.insert(order);
            if (result > 0) {
                return Result.success("添加订单成功");
            }
            return Result.error("添加订单失败");
        } catch (Exception e) {
            return Result.error("添加订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新订单
     */
    public Result<String> updateOrder(Order order) {
        try {
            order.setUpdateTime(LocalDateTime.now());
            int result = orderMapper.update(order);
            if (result > 0) {
                return Result.success("更新订单成功");
            }
            return Result.error("更新订单失败");
        } catch (Exception e) {
            return Result.error("更新订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除订单
     */
    public Result<String> deleteOrder(Long id) {
        try {
            int result = orderMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除订单成功");
            }
            return Result.error("删除订单失败");
        } catch (Exception e) {
            return Result.error("删除订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新订单状态
     */
    public Result<String> updateOrderStatus(Long id, Integer status) {
        try {
            int result = orderMapper.updateStatus(id, status);
            if (result > 0) {
                return Result.success("更新订单状态成功");
            }
            return Result.error("更新订单状态失败");
        } catch (Exception e) {
            return Result.error("更新订单状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据用户ID查询订单
     */
    public Result<List<Order>> getOrdersByUserId(Long userId) {
        try {
            List<Order> orders = orderMapper.selectByUserId(userId);
            return Result.success(orders);
        } catch (Exception e) {
            return Result.error("查询用户订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据状态查询订单
     */
    public Result<List<Order>> getOrdersByStatus(Integer status) {
        try {
            List<Order> orders = orderMapper.selectByStatus(status);
            return Result.success(orders);
        } catch (Exception e) {
            return Result.error("查询订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }
}
