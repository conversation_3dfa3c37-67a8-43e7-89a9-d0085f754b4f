<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.ProductMapper">

    <!-- 结果映射 -->
    <resultMap id="ProductResultMap" type="com.example.entity.Product">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="brand" column="brand"/>
        <result property="description" column="description"/>
        <result property="detail" column="detail"/>
        <result property="mainImage" column="main_image"/>
        <result property="images" column="images"/>
        <result property="originalPrice" column="original_price"/>
        <result property="currentPrice" column="current_price"/>
        <result property="costPrice" column="cost_price"/>
        <result property="stock" column="stock"/>
        <result property="warningStock" column="warning_stock"/>
        <result property="sales" column="sales"/>
        <result property="weight" column="weight"/>
        <result property="specifications" column="specifications"/>
        <result property="tags" column="tags"/>
        <result property="isRecommend" column="is_recommend"/>
        <result property="isNew" column="is_new"/>
        <result property="isHot" column="is_hot"/>
        <result property="status" column="status"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 查询所有商品 -->
    <select id="selectAll" resultMap="ProductResultMap">
        SELECT * FROM product ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据ID查询商品 -->
    <select id="selectById" resultMap="ProductResultMap">
        SELECT * FROM product WHERE id = #{id}
    </select>

    <!-- 根据商品编码查询商品 -->
    <select id="selectByCode" resultMap="ProductResultMap">
        SELECT * FROM product WHERE code = #{code}
    </select>

    <!-- 插入商品 -->
    <insert id="insert" parameterType="com.example.entity.Product" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO product (name, code, category_id, category_name, brand, description, detail, main_image, images,
                           original_price, current_price, cost_price, stock, warning_stock, sales, weight,
                           specifications, tags, is_recommend, is_new, is_hot, status, sort, create_time, update_time, remark)
        VALUES (#{name}, #{code}, #{categoryId}, #{categoryName}, #{brand}, #{description}, #{detail}, #{mainImage}, #{images},
                #{originalPrice}, #{currentPrice}, #{costPrice}, #{stock}, #{warningStock}, #{sales}, #{weight},
                #{specifications}, #{tags}, #{isRecommend}, #{isNew}, #{isHot}, #{status}, #{sort}, #{createTime}, #{updateTime}, #{remark})
    </insert>

    <!-- 更新商品 -->
    <update id="update" parameterType="com.example.entity.Product">
        UPDATE product SET
            name = #{name},
            code = #{code},
            category_id = #{categoryId},
            category_name = #{categoryName},
            brand = #{brand},
            description = #{description},
            detail = #{detail},
            main_image = #{mainImage},
            images = #{images},
            original_price = #{originalPrice},
            current_price = #{currentPrice},
            cost_price = #{costPrice},
            stock = #{stock},
            warning_stock = #{warningStock},
            weight = #{weight},
            specifications = #{specifications},
            tags = #{tags},
            is_recommend = #{isRecommend},
            is_new = #{isNew},
            is_hot = #{isHot},
            status = #{status},
            sort = #{sort},
            update_time = #{updateTime},
            remark = #{remark}
        WHERE id = #{id}
    </update>

    <!-- 删除商品 -->
    <delete id="deleteById">
        DELETE FROM product WHERE id = #{id}
    </delete>

    <!-- 根据分类查询商品 -->
    <select id="selectByCategoryId" resultMap="ProductResultMap">
        SELECT * FROM product WHERE category_id = #{categoryId} AND status = 1 ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据状态查询商品 -->
    <select id="selectByStatus" resultMap="ProductResultMap">
        SELECT * FROM product WHERE status = #{status} ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询推荐商品 -->
    <select id="selectRecommended" resultMap="ProductResultMap">
        SELECT * FROM product WHERE is_recommend = 1 AND status = 1 ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询新品 -->
    <select id="selectNew" resultMap="ProductResultMap">
        SELECT * FROM product WHERE is_new = 1 AND status = 1 ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询热销商品 -->
    <select id="selectHot" resultMap="ProductResultMap">
        SELECT * FROM product WHERE is_hot = 1 AND status = 1 ORDER BY sales DESC, create_time DESC
    </select>

    <!-- 分页查询商品 -->
    <select id="selectByPage" resultMap="ProductResultMap">
        SELECT * FROM product ORDER BY sort ASC, create_time DESC LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 根据关键词搜索商品 -->
    <select id="searchByKeyword" resultMap="ProductResultMap">
        SELECT * FROM product 
        WHERE (name LIKE CONCAT('%', #{keyword}, '%') 
               OR description LIKE CONCAT('%', #{keyword}, '%')
               OR tags LIKE CONCAT('%', #{keyword}, '%'))
        AND status = 1
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 统计商品总数 -->
    <select id="countTotal" resultType="int">
        SELECT COUNT(*) FROM product
    </select>

    <!-- 统计库存不足商品数量 -->
    <select id="countLowStock" resultType="int">
        SELECT COUNT(*) FROM product WHERE stock &lt;= warning_stock
    </select>

    <!-- 更新库存 -->
    <update id="updateStock">
        UPDATE product SET stock = #{stock}, update_time = CURRENT_TIMESTAMP WHERE id = #{id}
    </update>

    <!-- 增加销量 -->
    <update id="increaseSales">
        UPDATE product SET sales = sales + #{quantity}, update_time = CURRENT_TIMESTAMP WHERE id = #{id}
    </update>

</mapper>
