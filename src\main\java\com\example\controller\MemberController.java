package com.example.controller;

import com.example.common.Result;
import com.example.entity.Member;
import com.example.service.MemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员控制器
 */
@RestController
@RequestMapping("/member")
public class MemberController {
    
    @Autowired
    private MemberService memberService;
    
    /**
     * 获取所有会员
     */
    @GetMapping("/list")
    public Result<List<Member>> getAllMembers() {
        return memberService.getAllMembers();
    }
    
    /**
     * 根据ID获取会员
     */
    @GetMapping("/{id}")
    public Result<Member> getMemberById(@PathVariable Long id) {
        return memberService.getMemberById(id);
    }
    
    /**
     * 添加会员
     */
    @PostMapping("/add")
    public Result<String> addMember(@Valid @RequestBody Member member) {
        return memberService.addMember(member);
    }
    
    /**
     * 更新会员
     */
    @PutMapping("/update")
    public Result<String> updateMember(@Valid @RequestBody Member member) {
        return memberService.updateMember(member);
    }
    
    /**
     * 删除会员
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteMember(@PathVariable Long id) {
        return memberService.deleteMember(id);
    }
    
    /**
     * 根据用户ID获取会员信息
     */
    @GetMapping("/user/{userId}")
    public Result<Member> getMemberByUserId(@PathVariable Long userId) {
        return memberService.getMemberByUserId(userId);
    }
    
    /**
     * 更新会员积分
     */
    @PutMapping("/{id}/points")
    public Result<String> updateMemberPoints(@PathVariable Long id, @RequestParam Integer points) {
        return memberService.updateMemberPoints(id, points);
    }
    
    /**
     * 升级会员等级
     */
    @PutMapping("/{id}/upgrade")
    public Result<String> upgradeMemberLevel(@PathVariable Long id, @RequestParam String level) {
        return memberService.upgradeMemberLevel(id, level);
    }
}
