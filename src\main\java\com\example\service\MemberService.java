package com.example.service;

import com.example.common.Result;
import com.example.entity.Member;
import com.example.mapper.MemberMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员服务层
 */
@Service
public class MemberService {
    
    @Autowired
    private MemberMapper memberMapper;
    
    /**
     * 查询所有会员
     */
    public Result<List<Member>> getAllMembers() {
        try {
            List<Member> members = memberMapper.selectAll();
            return Result.success(members);
        } catch (Exception e) {
            return Result.error("查询会员列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询会员
     */
    public Result<Member> getMemberById(Long id) {
        try {
            Member member = memberMapper.selectById(id);
            if (member != null) {
                return Result.success(member);
            }
            return Result.error("会员不存在");
        } catch (Exception e) {
            return Result.error("查询会员失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加会员
     */
    public Result<String> addMember(Member member) {
        try {
            // 设置创建时间
            member.setCreateTime(LocalDateTime.now());
            member.setUpdateTime(LocalDateTime.now());
            
            // 设置默认值
            if (member.getLevel() == null) {
                member.setLevel("BRONZE"); // 默认青铜会员
            }
            if (member.getPoints() == null) {
                member.setPoints(0);
            }
            if (member.getStatus() == null) {
                member.setStatus(1); // 默认正常状态
            }
            
            int result = memberMapper.insert(member);
            if (result > 0) {
                return Result.success("添加会员成功");
            }
            return Result.error("添加会员失败");
        } catch (Exception e) {
            return Result.error("添加会员失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新会员
     */
    public Result<String> updateMember(Member member) {
        try {
            member.setUpdateTime(LocalDateTime.now());
            int result = memberMapper.update(member);
            if (result > 0) {
                return Result.success("更新会员成功");
            }
            return Result.error("更新会员失败");
        } catch (Exception e) {
            return Result.error("更新会员失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除会员
     */
    public Result<String> deleteMember(Long id) {
        try {
            int result = memberMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除会员成功");
            }
            return Result.error("删除会员失败");
        } catch (Exception e) {
            return Result.error("删除会员失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据用户ID查询会员
     */
    public Result<Member> getMemberByUserId(Long userId) {
        try {
            Member member = memberMapper.selectByUserId(userId);
            if (member != null) {
                return Result.success(member);
            }
            return Result.error("会员不存在");
        } catch (Exception e) {
            return Result.error("查询会员失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新会员积分
     */
    public Result<String> updateMemberPoints(Long id, Integer points) {
        try {
            int result = memberMapper.updatePoints(id, points);
            if (result > 0) {
                return Result.success("更新积分成功");
            }
            return Result.error("更新积分失败");
        } catch (Exception e) {
            return Result.error("更新积分失败: " + e.getMessage());
        }
    }
    
    /**
     * 升级会员等级
     */
    public Result<String> upgradeMemberLevel(Long id, String level) {
        try {
            int result = memberMapper.updateLevel(id, level);
            if (result > 0) {
                return Result.success("升级会员等级成功");
            }
            return Result.error("升级会员等级失败");
        } catch (Exception e) {
            return Result.error("升级会员等级失败: " + e.getMessage());
        }
    }
}
