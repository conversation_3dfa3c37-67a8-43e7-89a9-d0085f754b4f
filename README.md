# 商品管理系统 - 分类管理模块

这是一个基于Spring Boot + MyBatis + MySQL的商品分类管理系统，实现了图片中显示的分类管理功能。

## 功能特性

- ✅ 分类列表展示（分类名称、图标、排序、状态）
- ✅ 分类的增删改查操作
- ✅ 分类数据的Excel导出功能
- ✅ 分类数据的Excel导入功能
- ✅ 响应式Web界面设计
- ✅ 状态管理（正常/禁用）

## 技术栈

- **后端**: Spring Boot 2.7.0, MyBatis, MySQL
- **前端**: Bootstrap 5, JavaScript, Thymeleaf
- **数据库**: MySQL 8.0+
- **构建工具**: Maven

## 项目结构

```
src/
├── main/
│   ├── java/com/example/
│   │   ├── ProductManagementApplication.java  # 启动类
│   │   ├── controller/
│   │   │   ├── CategoryController.java        # 分类控制器
│   │   │   └── IndexController.java           # 首页控制器
│   │   ├── entity/
│   │   │   └── Category.java                  # 分类实体类
│   │   ├── mapper/
│   │   │   └── CategoryMapper.java            # 数据访问层
│   │   └── service/
│   │       ├── CategoryService.java           # 服务接口
│   │       └── impl/CategoryServiceImpl.java  # 服务实现
│   └── resources/
│       ├── mapper/CategoryMapper.xml          # MyBatis映射文件
│       ├── static/js/category.js              # 前端JavaScript
│       ├── templates/category/index.html      # 分类管理页面
│       ├── sql/init.sql                       # 数据库初始化脚本
│       └── application.yml                    # 应用配置
```

## 快速开始

### 1. 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE product_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p product_management < src/main/resources/sql/init.sql
```

3. 修改数据库连接配置（如需要）：
编辑 `src/main/resources/application.yml` 文件中的数据库连接信息。

### 3. 运行项目

1. 编译项目：
```bash
mvn clean compile
```

2. 运行项目：
```bash
mvn spring-boot:run
```

3. 访问应用：
打开浏览器访问 `http://localhost:8080`

## 功能说明

### 分类管理界面

- **分类列表**: 显示所有分类的名称、图标、排序和状态
- **添加分类**: 点击"添加分类"按钮可以新增分类
- **编辑分类**: 点击"编辑"按钮可以修改分类信息
- **删除分类**: 点击"删除"按钮可以删除分类
- **导出功能**: 点击"导出"按钮可以将分类数据导出为Excel文件
- **导入功能**: 点击"导入"按钮可以从Excel文件导入分类数据

### 初始数据

系统预置了以下分类数据：
- 玩具乐器 🎮
- 汽车用品 🚗  
- 家居家装 🏠
- 厨房餐饮 🍳
- 个护化妆 💄

## API接口

### 分类管理接口

- `GET /category/index` - 分类管理页面
- `GET /category/list` - 获取分类列表
- `POST /category/add` - 添加分类
- `POST /category/update` - 更新分类
- `POST /category/delete/{id}` - 删除分类
- `GET /category/export` - 导出分类数据
- `POST /category/import` - 导入分类数据

## 开发说明

### 添加新功能

1. 在 `Category` 实体类中添加新字段
2. 更新 `CategoryMapper.xml` 中的SQL语句
3. 在 `CategoryService` 中添加新的业务方法
4. 在 `CategoryController` 中添加新的接口
5. 更新前端页面和JavaScript

### 数据库扩展

如需添加新表，请在 `src/main/resources/sql/` 目录下创建相应的SQL文件。

## 注意事项

- 确保MySQL服务正在运行
- 检查数据库连接配置是否正确
- 导入Excel文件时，请确保文件格式正确
- 建议在生产环境中修改默认的数据库密码

## 许可证

MIT License
