package com.example.service;

import com.example.mapper.UserMapper;
import com.example.mapper.ProductMapper;
import com.example.mapper.OrderMapper;
import com.example.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 统计分析服务层
 */
@Service
public class StatisticsService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private OrderMapper orderMapper;
    
    /**
     * 获取系统概览统计数据
     */
    public Result<Map<String, Object>> getOverviewStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 用户统计
            int totalUsers = userMapper.countTotal();
            statistics.put("totalUsers", totalUsers);
            
            // 商品统计
            int totalProducts = productMapper.countTotal();
            int lowStockProducts = productMapper.countLowStock();
            statistics.put("totalProducts", totalProducts);
            statistics.put("lowStockProducts", lowStockProducts);
            
            // 订单统计
            int totalOrders = orderMapper.countTotal();
            int todayOrders = orderMapper.countToday();
            statistics.put("totalOrders", totalOrders);
            statistics.put("todayOrders", todayOrders);
            
            // 销售额统计
            BigDecimal todaySales = orderMapper.sumTodayAmount();
            BigDecimal monthSales = orderMapper.sumMonthAmount();
            statistics.put("todaySales", todaySales != null ? todaySales : BigDecimal.ZERO);
            statistics.put("monthSales", monthSales != null ? monthSales : BigDecimal.ZERO);
            
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取销售趋势数据
     */
    public Result<Map<String, Object>> getSalesTrend() {
        try {
            Map<String, Object> trendData = new HashMap<>();
            
            // 这里可以实现具体的销售趋势查询逻辑
            // 暂时返回模拟数据
            String[] months = {"1月", "2月", "3月", "4月", "5月", "6月"};
            BigDecimal[] sales = {
                new BigDecimal("12000"),
                new BigDecimal("19000"),
                new BigDecimal("15000"),
                new BigDecimal("25000"),
                new BigDecimal("22000"),
                new BigDecimal("30000")
            };
            
            trendData.put("months", months);
            trendData.put("sales", sales);
            
            return Result.success(trendData);
        } catch (Exception e) {
            return Result.error("获取销售趋势失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取商品分类销售统计
     */
    public Result<Map<String, Object>> getCategorySalesStatistics() {
        try {
            Map<String, Object> categoryStats = new HashMap<>();
            
            // 这里可以实现具体的分类销售统计逻辑
            // 暂时返回模拟数据
            String[] categories = {"Electronics", "Fashion", "Home & Garden", "Sports & Outdoors", "Beauty & Care"};
            BigDecimal[] amounts = {
                new BigDecimal("45000"),
                new BigDecimal("32000"),
                new BigDecimal("28000"),
                new BigDecimal("18000"),
                new BigDecimal("15000")
            };
            
            categoryStats.put("categories", categories);
            categoryStats.put("amounts", amounts);
            
            return Result.success(categoryStats);
        } catch (Exception e) {
            return Result.error("获取分类销售统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户增长统计
     */
    public Result<Map<String, Object>> getUserGrowthStatistics() {
        try {
            Map<String, Object> userGrowth = new HashMap<>();
            
            // 这里可以实现具体的用户增长统计逻辑
            // 暂时返回模拟数据
            String[] dates = {"2024-01", "2024-02", "2024-03", "2024-04", "2024-05", "2024-06"};
            Integer[] newUsers = {120, 150, 180, 200, 230, 250};
            Integer[] totalUsers = {120, 270, 450, 650, 880, 1130};
            
            userGrowth.put("dates", dates);
            userGrowth.put("newUsers", newUsers);
            userGrowth.put("totalUsers", totalUsers);
            
            return Result.success(userGrowth);
        } catch (Exception e) {
            return Result.error("获取用户增长统计失败: " + e.getMessage());
        }
    }
}
