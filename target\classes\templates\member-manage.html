<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 会员管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .member-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        .level-badge {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">👑 会员管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 添加会员
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportMembers()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 会员统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">总会员数</h6>
                                        <h3 id="totalMembers">1,234</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">青铜会员</h6>
                                        <h3 id="bronzeMembers">856</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-medal fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">白银会员</h6>
                                        <h3 id="silverMembers">267</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-medal fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">黄金会员</h6>
                                        <h3 id="goldMembers">111</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-crown fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索会员姓名或手机号">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="levelFilter">
                                    <option value="">全部等级</option>
                                    <option value="BRONZE">青铜会员</option>
                                    <option value="SILVER">白银会员</option>
                                    <option value="GOLD">黄金会员</option>
                                    <option value="DIAMOND">钻石会员</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary" onclick="searchMembers()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 会员列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">会员列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>头像</th>
                                        <th>会员信息</th>
                                        <th>联系方式</th>
                                        <th>会员等级</th>
                                        <th>积分</th>
                                        <th>消费金额</th>
                                        <th>状态</th>
                                        <th>加入时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="memberTableBody">
                                    <!-- 会员数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="会员分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadMembers();
        });

        // 加载会员列表
        function loadMembers() {
            fetch('/member/list')
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        displayMembers(result.data);
                    } else {
                        // 显示模拟数据
                        displayMembers(getMockMembers());
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 显示模拟数据
                    displayMembers(getMockMembers());
                });
        }

        // 显示会员列表
        function displayMembers(members) {
            const tbody = document.getElementById('memberTableBody');
            tbody.innerHTML = '';

            members.forEach(member => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <img src="${member.avatar || '/images/default-avatar.png'}" 
                             alt="${member.name}" class="member-avatar">
                    </td>
                    <td>
                        <div>
                            <strong>${member.name}</strong><br>
                            <small class="text-muted">ID: ${member.id}</small>
                        </div>
                    </td>
                    <td>
                        <div>
                            ${member.email || '未设置'}<br>
                            <small class="text-muted">${member.phone || '未设置'}</small>
                        </div>
                    </td>
                    <td>
                        <span class="badge ${getLevelBadgeClass(member.level)} level-badge">
                            ${getLevelText(member.level)}
                        </span>
                    </td>
                    <td>
                        <strong class="text-warning">${member.points}</strong>
                    </td>
                    <td>
                        <strong class="text-success">¥${member.totalAmount || 0}</strong>
                    </td>
                    <td>
                        <span class="badge ${member.status === 1 ? 'bg-success' : 'bg-secondary'}">
                            ${member.status === 1 ? '正常' : '禁用'}
                        </span>
                    </td>
                    <td>
                        <small>${formatDateTime(member.createTime)}</small>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editMember(${member.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="adjustPoints(${member.id})">
                            <i class="fas fa-coins"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMember(${member.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取等级徽章样式
        function getLevelBadgeClass(level) {
            switch(level) {
                case 'BRONZE': return 'bg-warning';
                case 'SILVER': return 'bg-info';
                case 'GOLD': return 'bg-success';
                case 'DIAMOND': return 'bg-primary';
                default: return 'bg-secondary';
            }
        }

        // 获取等级文本
        function getLevelText(level) {
            switch(level) {
                case 'BRONZE': return '青铜会员';
                case 'SILVER': return '白银会员';
                case 'GOLD': return '黄金会员';
                case 'DIAMOND': return '钻石会员';
                default: return '普通会员';
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleDateString('zh-CN');
        }

        // 编辑会员
        function editMember(id) {
            alert('编辑会员功能开发中...');
        }

        // 调整积分
        function adjustPoints(id) {
            const points = prompt('请输入要调整的积分数量（正数为增加，负数为减少）：');
            if (points !== null && !isNaN(points)) {
                alert(`积分调整功能开发中... 会员ID: ${id}, 积分变化: ${points}`);
            }
        }

        // 删除会员
        function deleteMember(id) {
            if (confirm('确定要删除这个会员吗？')) {
                alert('删除会员功能开发中...');
            }
        }

        // 搜索会员
        function searchMembers() {
            alert('搜索功能开发中...');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('levelFilter').value = '';
            document.getElementById('statusFilter').value = '';
            loadMembers();
        }

        // 显示添加模态框
        function showAddModal() {
            alert('添加会员功能开发中...');
        }

        // 导出会员
        function exportMembers() {
            alert('导出功能开发中...');
        }

        // 获取模拟会员数据
        function getMockMembers() {
            return [
                {
                    id: 1,
                    name: '张三',
                    email: '<EMAIL>',
                    phone: '138****8888',
                    level: 'GOLD',
                    points: 2580,
                    totalAmount: 15680.50,
                    status: 1,
                    createTime: '2024-01-15T10:30:00'
                },
                {
                    id: 2,
                    name: '李四',
                    email: '<EMAIL>',
                    phone: '139****9999',
                    level: 'SILVER',
                    points: 1250,
                    totalAmount: 8920.00,
                    status: 1,
                    createTime: '2024-02-20T14:15:00'
                },
                {
                    id: 3,
                    name: '王五',
                    email: '<EMAIL>',
                    phone: '137****7777',
                    level: 'BRONZE',
                    points: 680,
                    totalAmount: 3450.00,
                    status: 1,
                    createTime: '2024-03-10T09:45:00'
                },
                {
                    id: 4,
                    name: '赵六',
                    email: '<EMAIL>',
                    phone: '136****6666',
                    level: 'DIAMOND',
                    points: 5280,
                    totalAmount: 28750.00,
                    status: 1,
                    createTime: '2023-12-05T16:20:00'
                }
            ];
        }
    </script>
</body>
</html>
