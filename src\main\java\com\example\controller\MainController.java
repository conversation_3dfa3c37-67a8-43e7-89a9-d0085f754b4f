package com.example.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 主控制器 - 处理页面路由
 */
@Controller
public class MainController {
    
    /**
     * 首页 - 重定向到仪表盘
     */
    @GetMapping("/main")
    public String index() {
        return "redirect:/dashboard";
    }
    
    /**
     * 仪表盘页面
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        model.addAttribute("title", "系统概览");
        return "dashboard";
    }
    
    /**
     * 分类管理页面
     */
    @GetMapping("/category")
    public String categoryManage(Model model) {
        model.addAttribute("title", "分类管理");
        return "category";
    }
    
    /**
     * 用户管理页面
     */
    @GetMapping("/user/manage")
    public String userManage(Model model) {
        model.addAttribute("title", "用户管理");
        return "user-manage";
    }
    
    /**
     * 商品管理页面
     */
    @GetMapping("/product/manage")
    public String productManage(Model model) {
        model.addAttribute("title", "商品管理");
        return "product-manage";
    }
    
    /**
     * 订单管理页面
     */
    @GetMapping("/order/manage")
    public String orderManage(Model model) {
        model.addAttribute("title", "订单管理");
        return "order-manage";
    }
    
    /**
     * 会员管理页面
     */
    @GetMapping("/member/manage")
    public String memberManage(Model model) {
        model.addAttribute("title", "会员管理");
        return "member-manage";
    }
    
    /**
     * 优惠券管理页面
     */
    @GetMapping("/coupon/manage")
    public String couponManage(Model model) {
        model.addAttribute("title", "优惠券管理");
        return "coupon-manage";
    }
    
    /**
     * 统计分析页面
     */
    @GetMapping("/statistics")
    public String statistics(Model model) {
        model.addAttribute("title", "统计分析");
        return "statistics";
    }
    
    /**
     * 系统设置页面
     */
    @GetMapping("/settings")
    public String settings(Model model) {
        model.addAttribute("title", "系统设置");
        return "settings";
    }
}
