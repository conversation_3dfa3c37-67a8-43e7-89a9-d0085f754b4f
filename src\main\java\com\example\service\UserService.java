package com.example.service;

import com.example.entity.User;
import com.example.mapper.UserMapper;
import com.example.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务层
 */
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * 查询所有用户
     */
    public Result<List<User>> getAllUsers() {
        try {
            List<User> users = userMapper.selectAll();
            // 清除密码信息
            users.forEach(user -> user.setPassword(null));
            return Result.success(users);
        } catch (Exception e) {
            return Result.error("查询用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询用户
     */
    public Result<User> getUserById(Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user != null) {
                user.setPassword(null); // 清除密码信息
                return Result.success(user);
            }
            return Result.error("用户不存在");
        } catch (Exception e) {
            return Result.error("查询用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户注册
     */
    public Result<String> register(User user) {
        try {
            // 检查用户名是否已存在
            if (userMapper.selectByUsername(user.getUsername()) != null) {
                return Result.error("用户名已存在");
            }
            
            // 检查邮箱是否已存在
            if (StringUtils.hasText(user.getEmail()) && userMapper.selectByEmail(user.getEmail()) != null) {
                return Result.error("邮箱已被注册");
            }
            
            // 加密密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            
            // 设置默认值
            user.setRole("USER");
            user.setStatus(1);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            int result = userMapper.insert(user);
            if (result > 0) {
                return Result.success("注册成功");
            }
            return Result.error("注册失败");
        } catch (Exception e) {
            return Result.error("注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    public Result<User> login(String username, String password) {
        try {
            User user = userMapper.selectByUsername(username);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            if (user.getStatus() == 0) {
                return Result.error("账户已被禁用");
            }
            
            if (!passwordEncoder.matches(password, user.getPassword())) {
                return Result.error("密码错误");
            }
            
            // 更新最后登录信息
            userMapper.updateLastLogin(user.getId(), LocalDateTime.now().toString(), "127.0.0.1");
            
            // 清除密码信息
            user.setPassword(null);
            return Result.success(user);
        } catch (Exception e) {
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加用户
     */
    public Result<String> addUser(User user) {
        try {
            // 检查用户名是否已存在
            if (userMapper.selectByUsername(user.getUsername()) != null) {
                return Result.error("用户名已存在");
            }
            
            // 加密密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            
            // 设置创建时间
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            int result = userMapper.insert(user);
            if (result > 0) {
                return Result.success("添加用户成功");
            }
            return Result.error("添加用户失败");
        } catch (Exception e) {
            return Result.error("添加用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户
     */
    public Result<String> updateUser(User user) {
        try {
            // 如果有新密码，需要加密
            if (StringUtils.hasText(user.getPassword())) {
                user.setPassword(passwordEncoder.encode(user.getPassword()));
            } else {
                // 如果没有新密码，保持原密码不变
                User existUser = userMapper.selectById(user.getId());
                user.setPassword(existUser.getPassword());
            }
            
            user.setUpdateTime(LocalDateTime.now());
            
            int result = userMapper.update(user);
            if (result > 0) {
                return Result.success("更新用户成功");
            }
            return Result.error("更新用户失败");
        } catch (Exception e) {
            return Result.error("更新用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    public Result<String> deleteUser(Long id) {
        try {
            int result = userMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除用户成功");
            }
            return Result.error("删除用户失败");
        } catch (Exception e) {
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }
}
