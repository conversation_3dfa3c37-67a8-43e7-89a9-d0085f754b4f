package com.example.service;

import com.example.entity.Category;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService {
    
    /**
     * 查询所有分类
     */
    List<Category> getAllCategories();
    
    /**
     * 根据ID查询分类
     */
    Category getCategoryById(Long id);
    
    /**
     * 添加分类
     */
    boolean addCategory(Category category);
    
    /**
     * 更新分类
     */
    boolean updateCategory(Category category);
    
    /**
     * 删除分类
     */
    boolean deleteCategory(Long id);
    
    /**
     * 根据状态查询分类
     */
    List<Category> getCategoriesByStatus(Integer status);
    
    /**
     * 导出分类数据到Excel
     */
    void exportCategories(HttpServletResponse response);
    
    /**
     * 从Excel导入分类数据
     */
    boolean importCategories(MultipartFile file);
}
