# 🛍️ 尚品甄选 - 电商管理系统

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.0-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Java](https://img.shields.io/badge/Java-8+-orange.svg)](https://www.oracle.com/java/)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

尚品甄选是一个功能完整的电商管理系统，提供商品管理、订单处理、用户管理、会员系统等全套电商解决方案。

## ✨ 功能特性

### 🏪 核心功能
- **用户管理** - 用户注册、登录、权限管理
- **商品管理** - 商品CRUD、库存管理、规格管理
- **分类管理** - 商品分类的层级管理
- **订单管理** - 订单处理、状态跟踪、支付管理
- **会员管理** - 会员等级、积分系统
- **营销管理** - 优惠券、促销活动
- **统计分析** - 销售报表、数据分析
- **系统设置** - 基础配置、参数管理

### 🎨 界面特色
- 现代化响应式设计
- 直观的管理后台
- 丰富的图表展示
- 移动端适配

### 🔧 技术特性
- RESTful API设计
- 数据导入导出
- 安全认证授权
- 实时数据统计

## 🚀 技术栈

### 后端技术
- **Spring Boot 2.7.0** - 主框架
- **Spring Security** - 安全框架
- **MyBatis** - ORM框架
- **H2 Database** - 内存数据库
- **Maven** - 项目管理

### 前端技术
- **Thymeleaf** - 模板引擎
- **Bootstrap 5** - UI框架
- **Font Awesome** - 图标库
- **Chart.js** - 图表库
- **jQuery** - JavaScript库

## 📦 快速开始

### 环境要求
- Java 8+
- Maven 3.6+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/shangpin-ecommerce.git
   cd shangpin-ecommerce
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **运行应用**
   ```bash
   mvn spring-boot:run
   ```

4. **访问系统**
   - 管理后台: http://localhost:8080
   - H2控制台: http://localhost:8080/h2-console

### 默认账户
- 用户名: `admin`
- 密码: `admin123`

## 📊 系统架构

```
尚品甄选电商管理系统
├── 用户管理模块
├── 商品管理模块
├── 订单管理模块
├── 会员管理模块
├── 营销管理模块
├── 统计分析模块
└── 系统设置模块
```

## 🗄️ 数据库设计

系统包含以下核心数据表：
- `user` - 用户表
- `category` - 分类表
- `product` - 商品表
- `order` - 订单表
- `order_item` - 订单商品表
- `member` - 会员表
- `coupon` - 优惠券表
- `user_coupon` - 用户优惠券表

## 🔗 API文档

### 用户管理API
- `GET /user/list` - 获取用户列表
- `POST /user/add` - 添加用户
- `PUT /user/update` - 更新用户
- `DELETE /user/{id}` - 删除用户

### 商品管理API
- `GET /product/list` - 获取商品列表
- `POST /product/add` - 添加商品
- `PUT /product/update` - 更新商品
- `DELETE /product/{id}` - 删除商品

### 统计分析API
- `GET /api/statistics/overview` - 系统概览统计
- `GET /api/statistics/sales-trend` - 销售趋势
- `GET /api/statistics/category-sales` - 分类销售统计

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目主页: https://github.com/your-username/shangpin-ecommerce
- 问题反馈: https://github.com/your-username/shangpin-ecommerce/issues
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
