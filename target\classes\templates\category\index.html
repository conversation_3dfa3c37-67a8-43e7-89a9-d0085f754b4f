<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理 - 分类管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            background-color: #2c3e50;
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: #bdc3c7;
            padding: 15px 20px;
            border-bottom: 1px solid #34495e;
        }
        .sidebar .nav-link:hover {
            background-color: #34495e;
            color: white;
        }
        .sidebar .nav-link.active {
            background-color: #3498db;
            color: white;
        }
        .main-content {
            background-color: #ecf0f1;
            min-height: 100vh;
        }
        .breadcrumb {
            background-color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .content-card {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .category-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .status-normal {
            background-color: #d4edda;
            color: #155724;
        }
        .status-disabled {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="p-3">
                    <h5><i class="fas fa-cube"></i> 商品管理</h5>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link" href="#"><i class="fas fa-home"></i> 工作台</a>
                    <a class="nav-link" href="#"><i class="fas fa-cogs"></i> 系统管理</a>
                    <a class="nav-link" href="#"><i class="fas fa-database"></i> 基础数据管理</a>
                    <a class="nav-link active" href="#"><i class="fas fa-tags"></i> 商品管理</a>
                    <a class="nav-link" href="#"><i class="fas fa-users"></i> 会员管理</a>
                    <a class="nav-link" href="#"><i class="fas fa-shopping-cart"></i> 订单管理</a>
                </nav>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 面包屑导航 -->
                <div class="breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="#">首页</a></li>
                            <li class="breadcrumb-item"><a href="#">商品管理</a></li>
                            <li class="breadcrumb-item active">分类管理</li>
                        </ol>
                    </nav>
                </div>
                
                <!-- 内容卡片 -->
                <div class="content-card">
                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">分类管理</h5>
                        <div>
                            <button class="btn btn-success btn-sm" onclick="exportData()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="showImportModal()">
                                <i class="fas fa-upload"></i> 导入
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 添加分类
                            </button>
                        </div>
                    </div>
                    
                    <!-- 分类表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>分类名称</th>
                                    <th>图标</th>
                                    <th>排序</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="categoryTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalTitle">添加分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">分类名称</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="categoryIcon" class="form-label">图标</label>
                            <input type="text" class="form-control" id="categoryIcon" placeholder="输入emoji或图标">
                        </div>
                        <div class="mb-3">
                            <label for="categorySort" class="form-label">排序</label>
                            <input type="number" class="form-control" id="categorySort" value="0">
                        </div>
                        <div class="mb-3">
                            <label for="categoryStatus" class="form-label">状态</label>
                            <select class="form-select" id="categoryStatus">
                                <option value="1">正常</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="categoryRemark" class="form-label">备注</label>
                            <textarea class="form-control" id="categoryRemark" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCategory()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入分类数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="importFile" class="form-label">选择Excel文件</label>
                            <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls" required>
                        </div>
                        <div class="alert alert-info">
                            <small>请上传Excel文件，文件格式：分类名称、图标、排序、状态、备注</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importData()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/category.js"></script>
</body>
</html>
