@echo off
echo 正在启动商品管理系统...
echo.

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装JDK 8或更高版本
    pause
    exit /b 1
)

echo.
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo.
echo 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 启动应用...
echo 请确保MySQL数据库已启动并执行了初始化脚本
echo 应用启动后请访问: http://localhost:8080
echo.
mvn spring-boot:run

pause
