package com.example.service;

import com.example.entity.Category;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分类服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class CategoryServiceTest {

    @Autowired
    private CategoryService categoryService;

    @Test
    public void testGetAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        assertNotNull(categories);
        System.out.println("分类数量: " + categories.size());
        
        for (Category category : categories) {
            System.out.println("分类: " + category.getName() + " - " + category.getIcon());
        }
    }

    @Test
    public void testAddCategory() {
        Category category = new Category();
        category.setName("测试分类");
        category.setIcon("🧪");
        category.setSort(99);
        category.setStatus(1);
        category.setRemark("这是一个测试分类");
        
        boolean result = categoryService.addCategory(category);
        assertTrue(result);
        System.out.println("添加分类成功，ID: " + category.getId());
    }

    @Test
    public void testGetCategoriesByStatus() {
        List<Category> normalCategories = categoryService.getCategoriesByStatus(1);
        assertNotNull(normalCategories);
        System.out.println("正常状态的分类数量: " + normalCategories.size());
        
        List<Category> disabledCategories = categoryService.getCategoriesByStatus(0);
        assertNotNull(disabledCategories);
        System.out.println("禁用状态的分类数量: " + disabledCategories.size());
    }
}
