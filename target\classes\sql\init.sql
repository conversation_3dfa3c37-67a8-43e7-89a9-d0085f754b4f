-- 创建数据库
CREATE DATABASE IF NOT EXISTS product_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE product_management;

-- 创建分类表
CREATE TABLE IF NOT EXISTS category (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    icon VARCHAR(200) COMMENT '分类图标',
    sort INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态 (0-禁用, 1-正常)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_status (status),
    INDEX idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 插入初始数据
INSERT INTO category (name, icon, sort, status, remark) VALUES
('玩具乐器', '🎮', 10, 1, '玩具和乐器类商品'),
('汽车用品', '🚗', 10, 1, '汽车相关用品'),
('家居家装', '🏠', 10, 1, '家居装修用品'),
('厨房餐饮', '🍳', 5, 1, '厨房和餐饮用品'),
('个护化妆', '💄', 4, 1, '个人护理和化妆品');
