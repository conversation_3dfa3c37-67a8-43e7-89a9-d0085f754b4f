package com.example.service;

import com.example.entity.Product;
import com.example.mapper.ProductMapper;
import com.example.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 商品服务层
 */
@Service
public class ProductService {
    
    @Autowired
    private ProductMapper productMapper;
    
    /**
     * 查询所有商品
     */
    public Result<List<Product>> getAllProducts() {
        try {
            List<Product> products = productMapper.selectAll();
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("查询商品列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询商品
     */
    public Result<Product> getProductById(Long id) {
        try {
            Product product = productMapper.selectById(id);
            if (product != null) {
                return Result.success(product);
            }
            return Result.error("商品不存在");
        } catch (Exception e) {
            return Result.error("查询商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加商品
     */
    public Result<String> addProduct(Product product) {
        try {
            // 生成商品编码
            if (!StringUtils.hasText(product.getCode())) {
                product.setCode("P" + System.currentTimeMillis());
            }
            
            // 检查商品编码是否已存在
            if (productMapper.selectByCode(product.getCode()) != null) {
                return Result.error("商品编码已存在");
            }
            
            // 设置默认值
            if (product.getStatus() == null) {
                product.setStatus(1); // 默认上架
            }
            if (product.getSort() == null) {
                product.setSort(0);
            }
            if (product.getSales() == null) {
                product.setSales(0);
            }
            if (product.getIsRecommend() == null) {
                product.setIsRecommend(0);
            }
            if (product.getIsNew() == null) {
                product.setIsNew(0);
            }
            if (product.getIsHot() == null) {
                product.setIsHot(0);
            }
            
            product.setCreateTime(LocalDateTime.now());
            product.setUpdateTime(LocalDateTime.now());
            
            int result = productMapper.insert(product);
            if (result > 0) {
                return Result.success("添加商品成功");
            }
            return Result.error("添加商品失败");
        } catch (Exception e) {
            return Result.error("添加商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新商品
     */
    public Result<String> updateProduct(Product product) {
        try {
            product.setUpdateTime(LocalDateTime.now());
            
            int result = productMapper.update(product);
            if (result > 0) {
                return Result.success("更新商品成功");
            }
            return Result.error("更新商品失败");
        } catch (Exception e) {
            return Result.error("更新商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除商品
     */
    public Result<String> deleteProduct(Long id) {
        try {
            int result = productMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除商品成功");
            }
            return Result.error("删除商品失败");
        } catch (Exception e) {
            return Result.error("删除商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据分类查询商品
     */
    public Result<List<Product>> getProductsByCategory(Long categoryId) {
        try {
            List<Product> products = productMapper.selectByCategoryId(categoryId);
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("查询分类商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 搜索商品
     */
    public Result<List<Product>> searchProducts(String keyword) {
        try {
            List<Product> products = productMapper.searchByKeyword(keyword);
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("搜索商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取推荐商品
     */
    public Result<List<Product>> getRecommendedProducts() {
        try {
            List<Product> products = productMapper.selectRecommended();
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("查询推荐商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取新品
     */
    public Result<List<Product>> getNewProducts() {
        try {
            List<Product> products = productMapper.selectNew();
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("查询新品失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取热销商品
     */
    public Result<List<Product>> getHotProducts() {
        try {
            List<Product> products = productMapper.selectHot();
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("查询热销商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新库存
     */
    public Result<String> updateStock(Long id, Integer stock) {
        try {
            int result = productMapper.updateStock(id, stock);
            if (result > 0) {
                return Result.success("更新库存成功");
            }
            return Result.error("更新库存失败");
        } catch (Exception e) {
            return Result.error("更新库存失败: " + e.getMessage());
        }
    }
}
