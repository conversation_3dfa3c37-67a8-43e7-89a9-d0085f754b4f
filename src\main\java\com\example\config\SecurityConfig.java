package com.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Spring Security配置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeRequests()
            .antMatchers("/", "/login", "/captcha", "/static/**", "/css/**", "/js/**", "/images/**").permitAll()
            .antMatchers("/user/register", "/user/login").permitAll()
            .antMatchers("/category/**", "/product/**").permitAll()
            .antMatchers("/dashboard", "/main").permitAll()
            .antMatchers("/user/manage", "/statistics", "/settings").permitAll()
            .antMatchers("/order/**", "/member/**", "/coupon/**").permitAll()
            .antMatchers("/api/**").permitAll()
            .antMatchers("/h2-console/**").permitAll()
            .anyRequest().permitAll() // 开发环境允许所有访问
            .and()
            .formLogin()
            .loginPage("/login")
            .defaultSuccessUrl("/dashboard", true)
            .and()
            .logout()
            .logoutUrl("/logout")
            .logoutSuccessUrl("/login")
            .and()
            .headers().frameOptions().disable(); // 允许H2控制台使用iframe
    }
}
