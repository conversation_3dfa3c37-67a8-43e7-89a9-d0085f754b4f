# 商品管理系统 - 快速启动指南

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

**Windows用户：**
```bash
双击运行 start.bat
```

**Linux/Mac用户：**
```bash
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

1. **编译项目**
```bash
mvn clean compile
```

2. **启动应用**
```bash
mvn spring-boot:run
```

3. **访问应用**
打开浏览器访问：http://localhost:8080

## 📋 前置要求

### 必需环境
- ✅ JDK 8 或更高版本
- ✅ Maven 3.6 或更高版本
- ✅ MySQL 8.0 或更高版本

### 环境检查
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查MySQL版本
mysql --version
```

## 🗄️ 数据库配置

### 1. 创建数据库
```sql
CREATE DATABASE product_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 执行初始化脚本
```bash
mysql -u root -p product_management < src/main/resources/sql/init.sql
```

### 3. 修改数据库连接（如需要）
编辑 `src/main/resources/application.yml` 文件：
```yaml
spring:
  datasource:
    url: *******************************************************************************************************************************
    username: root
    password: 你的密码
```

## 🎯 功能演示

启动成功后，您可以：

1. **查看分类列表** - 系统预置了5个分类
2. **添加新分类** - 点击"添加分类"按钮
3. **编辑分类** - 点击分类行的"编辑"按钮
4. **删除分类** - 点击分类行的"删除"按钮
5. **导出数据** - 点击"导出"按钮下载Excel文件
6. **导入数据** - 点击"导入"按钮上传Excel文件

## 🔧 常见问题

### Q: 编译失败怎么办？
A: 检查网络连接，Maven需要下载依赖包。如果网络较慢，请耐心等待。

### Q: 启动时报数据库连接错误？
A: 
1. 确保MySQL服务已启动
2. 检查数据库连接配置是否正确
3. 确保数据库已创建并执行了初始化脚本

### Q: 页面显示不正常？
A: 
1. 检查浏览器控制台是否有JavaScript错误
2. 确保网络连接正常（页面使用了CDN资源）
3. 尝试清除浏览器缓存

### Q: 端口8080被占用？
A: 修改 `application.yml` 中的端口配置：
```yaml
server:
  port: 8081  # 改为其他端口
```

## 📞 技术支持

如果遇到问题，请检查：
1. 控制台日志输出
2. 数据库连接状态
3. 网络连接情况

## 🎉 成功标志

当您看到以下日志时，说明启动成功：
```
Started ProductManagementApplication in X.XXX seconds
```

然后访问 http://localhost:8080 即可看到分类管理界面！
