<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .captcha-display {
            font-size: 24px;
            font-weight: bold;
            padding: 10px;
            background: #f5f5f5;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            margin: 10px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>验证码功能测试</h1>
    
    <div class="test-section">
        <h3>1. 前端生成验证码测试</h3>
        <div>当前验证码: <span class="captcha-display" id="frontendCaptcha" onclick="refreshFrontendCaptcha()">点击生成</span></div>
        <button onclick="refreshFrontendCaptcha()">刷新前端验证码</button>
        <div id="frontendResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 后端API测试</h3>
        <div>API验证码: <span class="captcha-display" id="apiCaptcha">点击测试</span></div>
        <button onclick="testApiCaptcha()">测试API验证码</button>
        <div id="apiResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 登录测试</h3>
        <form onsubmit="testLogin(event)">
            <div>
                <label>用户名: </label>
                <input type="text" id="username" value="admin" required>
            </div>
            <br>
            <div>
                <label>密码: </label>
                <input type="password" id="password" value="admin123" required>
            </div>
            <br>
            <div>
                <label>验证码: </label>
                <input type="text" id="captchaInput" required>
                <span class="captcha-display" id="loginCaptcha" onclick="refreshLoginCaptcha()">点击生成</span>
            </div>
            <br>
            <button type="submit">测试登录</button>
        </form>
        <div id="loginResult" class="result"></div>
    </div>

    <script>
        let currentCaptcha = '';
        
        // 生成验证码
        function generateCaptcha() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let captcha = '';
            for (let i = 0; i < 4; i++) {
                captcha += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return captcha;
        }
        
        // 刷新前端验证码
        function refreshFrontendCaptcha() {
            const captcha = generateCaptcha();
            document.getElementById('frontendCaptcha').textContent = captcha;
            document.getElementById('frontendResult').innerHTML = `<div class="success">前端验证码生成成功: ${captcha}</div>`;
        }
        
        // 测试API验证码
        function testApiCaptcha() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div>正在测试API...</div>';
            
            fetch('/captcha')
                .then(response => {
                    console.log('API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('API响应数据:', data);
                    if (data && data.code === 200 && data.data) {
                        document.getElementById('apiCaptcha').textContent = data.data;
                        resultDiv.innerHTML = `<div class="success">API验证码获取成功: ${data.data}</div>`;
                    } else {
                        throw new Error('API返回数据格式错误: ' + JSON.stringify(data));
                    }
                })
                .catch(error => {
                    console.error('API测试失败:', error);
                    resultDiv.innerHTML = `<div class="error">API测试失败: ${error.message}</div>`;
                });
        }
        
        // 刷新登录验证码
        function refreshLoginCaptcha() {
            currentCaptcha = generateCaptcha();
            document.getElementById('loginCaptcha').textContent = currentCaptcha;
        }
        
        // 测试登录
        function testLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captchaInput').value;
            const resultDiv = document.getElementById('loginResult');
            
            // 验证验证码
            if (captcha.toUpperCase() !== currentCaptcha.toUpperCase()) {
                resultDiv.innerHTML = '<div class="error">验证码错误</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div>正在登录...</div>';
            
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            formData.append('captcha', captcha);
            
            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 200) {
                    resultDiv.innerHTML = `<div class="success">登录成功: ${result.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">登录失败: ${result.message}</div>`;
                }
            })
            .catch(error => {
                console.error('登录测试失败:', error);
                resultDiv.innerHTML = `<div class="error">登录测试失败: ${error.message}</div>`;
            });
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshFrontendCaptcha();
            refreshLoginCaptcha();
        });
    </script>
</body>
</html>
