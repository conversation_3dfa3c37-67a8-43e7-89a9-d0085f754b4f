com\example\entity\Order.class
com\example\entity\User.class
com\example\controller\AuthController.class
com\example\service\StatisticsService.class
com\example\controller\MainController.class
com\example\entity\Product.class
com\example\controller\ProductController.class
com\example\controller\StatisticsController.class
com\example\controller\OrderController.class
com\example\ProductManagementApplication.class
com\example\service\UserService.class
com\example\entity\Coupon.class
com\example\controller\CategoryController.class
com\example\controller\MemberController.class
com\example\controller\UserController.class
com\example\mapper\ProductMapper.class
com\example\mapper\UserMapper.class
com\example\entity\OrderItem.class
com\example\service\impl\CategoryServiceImpl.class
com\example\mapper\OrderMapper.class
com\example\service\ProductService.class
com\example\common\Result.class
com\example\mapper\MemberMapper.class
com\example\config\SecurityConfig.class
com\example\mapper\CategoryMapper.class
com\example\config\WebConfig.class
com\example\entity\Category.class
com\example\entity\Member.class
com\example\service\MemberService.class
com\example\service\CategoryService.class
com\example\service\OrderService.class
