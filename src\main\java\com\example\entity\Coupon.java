package com.example.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券实体类
 */
@Data
public class Coupon {
    
    /**
     * 优惠券ID
     */
    private Long id;
    
    /**
     * 优惠券名称
     */
    private String name;
    
    /**
     * 优惠券编码
     */
    private String code;
    
    /**
     * 优惠券类型 (DISCOUNT-折扣券, CASH-现金券, SHIPPING-包邮券)
     */
    private String type;
    
    /**
     * 优惠金额/折扣
     */
    private BigDecimal value;
    
    /**
     * 最低消费金额
     */
    private BigDecimal minAmount;
    
    /**
     * 最大优惠金额
     */
    private BigDecimal maxAmount;
    
    /**
     * 发放数量
     */
    private Integer totalCount;
    
    /**
     * 已使用数量
     */
    private Integer usedCount;
    
    /**
     * 每人限领数量
     */
    private Integer limitPerUser;
    
    /**
     * 有效期开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 有效期结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 状态 (0-禁用, 1-正常)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
