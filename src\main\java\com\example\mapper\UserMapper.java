package com.example.mapper;

import com.example.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper {
    
    /**
     * 查询所有用户
     */
    List<User> selectAll();
    
    /**
     * 根据ID查询用户
     */
    User selectById(@Param("id") Long id);
    
    /**
     * 根据用户名查询用户
     */
    User selectByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱查询用户
     */
    User selectByEmail(@Param("email") String email);
    
    /**
     * 插入用户
     */
    int insert(User user);
    
    /**
     * 更新用户
     */
    int update(User user);
    
    /**
     * 删除用户
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据角色查询用户
     */
    List<User> selectByRole(@Param("role") String role);
    
    /**
     * 根据状态查询用户
     */
    List<User> selectByStatus(@Param("status") Integer status);
    
    /**
     * 分页查询用户
     */
    List<User> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 统计用户总数
     */
    int countTotal();
    
    /**
     * 更新最后登录信息
     */
    int updateLastLogin(@Param("id") Long id, @Param("loginTime") String loginTime, @Param("loginIp") String loginIp);
}
