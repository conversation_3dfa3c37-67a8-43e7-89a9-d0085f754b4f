package com.example.service.impl;

import com.example.entity.Category;
import com.example.mapper.CategoryMapper;
import com.example.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 分类服务实现类
 */
@Slf4j
@Service
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public List<Category> getAllCategories() {
        return categoryMapper.selectAll();
    }

    @Override
    public Category getCategoryById(Long id) {
        return categoryMapper.selectById(id);
    }

    @Override
    public boolean addCategory(Category category) {
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        return categoryMapper.insert(category) > 0;
    }

    @Override
    public boolean updateCategory(Category category) {
        category.setUpdateTime(LocalDateTime.now());
        return categoryMapper.update(category) > 0;
    }

    @Override
    public boolean deleteCategory(Long id) {
        return categoryMapper.deleteById(id) > 0;
    }

    @Override
    public List<Category> getCategoriesByStatus(Integer status) {
        return categoryMapper.selectByStatus(status);
    }

    @Override
    public void exportCategories(HttpServletResponse response) {
        try {
            List<Category> categories = getAllCategories();
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("分类数据");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "分类名称", "图标", "排序", "状态", "创建时间", "更新时间", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 填充数据
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < categories.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Category category = categories.get(i);
                
                row.createCell(0).setCellValue(category.getId());
                row.createCell(1).setCellValue(category.getName());
                row.createCell(2).setCellValue(category.getIcon());
                row.createCell(3).setCellValue(category.getSort());
                row.createCell(4).setCellValue(category.getStatus() == 1 ? "正常" : "禁用");
                row.createCell(5).setCellValue(category.getCreateTime().format(formatter));
                row.createCell(6).setCellValue(category.getUpdateTime().format(formatter));
                row.createCell(7).setCellValue(category.getRemark());
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=categories.xlsx");
            
            // 写入响应
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            log.error("导出分类数据失败", e);
        }
    }

    @Override
    public boolean importCategories(MultipartFile file) {
        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            List<Category> categories = new ArrayList<>();
            
            // 跳过标题行，从第二行开始读取
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                Category category = new Category();
                category.setName(getCellStringValue(row.getCell(1)));
                category.setIcon(getCellStringValue(row.getCell(2)));
                category.setSort((int) row.getCell(3).getNumericCellValue());
                String statusStr = getCellStringValue(row.getCell(4));
                category.setStatus("正常".equals(statusStr) ? 1 : 0);
                category.setRemark(getCellStringValue(row.getCell(7)));
                category.setCreateTime(LocalDateTime.now());
                category.setUpdateTime(LocalDateTime.now());
                
                categories.add(category);
            }
            
            workbook.close();
            
            // 批量插入
            return categoryMapper.batchInsert(categories) > 0;
            
        } catch (IOException e) {
            log.error("导入分类数据失败", e);
            return false;
        }
    }
    
    private String getCellStringValue(Cell cell) {
        if (cell == null) return "";
        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            return String.valueOf((int) cell.getNumericCellValue());
        }
        return "";
    }
}
