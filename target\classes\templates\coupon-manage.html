<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 优惠券管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .coupon-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .coupon-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🎫 优惠券管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 创建优惠券
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportCoupons()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 优惠券统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">总优惠券</h6>
                                        <h3 id="totalCoupons">15</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-ticket-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">有效优惠券</h6>
                                        <h3 id="activeCoupons">12</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">已使用</h6>
                                        <h3 id="usedCoupons">1,256</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-shopping-bag fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">节省金额</h6>
                                        <h3 id="savedAmount">¥25,680</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-piggy-bank fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索优惠券名称或代码">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="typeFilter">
                                    <option value="">全部类型</option>
                                    <option value="CASH">现金券</option>
                                    <option value="DISCOUNT">折扣券</option>
                                    <option value="SHIPPING">包邮券</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">有效</option>
                                    <option value="0">无效</option>
                                    <option value="2">已过期</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary" onclick="searchCoupons()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 优惠券列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">优惠券列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>优惠券信息</th>
                                        <th>类型</th>
                                        <th>面值/折扣</th>
                                        <th>使用条件</th>
                                        <th>发放/使用</th>
                                        <th>有效期</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="couponTableBody">
                                    <!-- 优惠券数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="优惠券分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadCoupons();
        });

        // 加载优惠券列表
        function loadCoupons() {
            // 显示模拟数据
            displayCoupons(getMockCoupons());
        }

        // 显示优惠券列表
        function displayCoupons(coupons) {
            const tbody = document.getElementById('couponTableBody');
            tbody.innerHTML = '';

            coupons.forEach(coupon => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div>
                            <strong>${coupon.name}</strong><br>
                            <small class="text-muted">代码: ${coupon.code}</small>
                        </div>
                    </td>
                    <td>
                        <span class="badge ${getTypeBadgeClass(coupon.type)}">
                            ${getTypeText(coupon.type)}
                        </span>
                    </td>
                    <td>
                        <span class="coupon-value">
                            ${getCouponValue(coupon)}
                        </span>
                    </td>
                    <td>
                        <small>
                            ${coupon.minAmount ? `满¥${coupon.minAmount}可用` : '无门槛'}
                            ${coupon.maxAmount ? `<br>最高¥${coupon.maxAmount}` : ''}
                        </small>
                    </td>
                    <td>
                        <div>
                            <small>发放: ${coupon.totalCount}</small><br>
                            <small>已用: <span class="text-success">${coupon.usedCount}</span></small>
                        </div>
                    </td>
                    <td>
                        <small>
                            ${formatDate(coupon.startTime)}<br>
                            至 ${formatDate(coupon.endTime)}
                        </small>
                    </td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(coupon.status)}">
                            ${getStatusText(coupon.status)}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editCoupon(${coupon.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewCouponUsers(${coupon.id})">
                            <i class="fas fa-users"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCoupon(${coupon.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取类型徽章样式
        function getTypeBadgeClass(type) {
            switch(type) {
                case 'CASH': return 'bg-success';
                case 'DISCOUNT': return 'bg-warning';
                case 'SHIPPING': return 'bg-info';
                default: return 'bg-secondary';
            }
        }

        // 获取类型文本
        function getTypeText(type) {
            switch(type) {
                case 'CASH': return '现金券';
                case 'DISCOUNT': return '折扣券';
                case 'SHIPPING': return '包邮券';
                default: return '未知';
            }
        }

        // 获取优惠券面值
        function getCouponValue(coupon) {
            switch(coupon.type) {
                case 'CASH':
                    return `¥${coupon.value}`;
                case 'DISCOUNT':
                    return `${(coupon.value * 10).toFixed(1)}折`;
                case 'SHIPPING':
                    return '包邮';
                default:
                    return coupon.value;
            }
        }

        // 获取状态徽章样式
        function getStatusBadgeClass(status) {
            switch(status) {
                case 1: return 'bg-success';
                case 0: return 'bg-secondary';
                case 2: return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 1: return '有效';
                case 0: return '无效';
                case 2: return '已过期';
                default: return '未知';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            return date.toLocaleDateString('zh-CN');
        }

        // 编辑优惠券
        function editCoupon(id) {
            alert('编辑优惠券功能开发中...');
        }

        // 查看优惠券用户
        function viewCouponUsers(id) {
            alert('查看优惠券用户功能开发中...');
        }

        // 删除优惠券
        function deleteCoupon(id) {
            if (confirm('确定要删除这个优惠券吗？')) {
                alert('删除优惠券功能开发中...');
            }
        }

        // 搜索优惠券
        function searchCoupons() {
            alert('搜索功能开发中...');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('statusFilter').value = '';
            loadCoupons();
        }

        // 显示添加模态框
        function showAddModal() {
            alert('创建优惠券功能开发中...');
        }

        // 导出优惠券
        function exportCoupons() {
            alert('导出功能开发中...');
        }

        // 获取模拟优惠券数据
        function getMockCoupons() {
            return [
                {
                    id: 1,
                    name: '新用户专享',
                    code: 'NEW2024',
                    type: 'CASH',
                    value: 50.00,
                    minAmount: 100.00,
                    maxAmount: null,
                    totalCount: 1000,
                    usedCount: 256,
                    startTime: '2024-01-01',
                    endTime: '2024-12-31',
                    status: 1
                },
                {
                    id: 2,
                    name: 'VIP会员折扣',
                    code: 'VIP2024',
                    type: 'DISCOUNT',
                    value: 0.85,
                    minAmount: 200.00,
                    maxAmount: 100.00,
                    totalCount: 500,
                    usedCount: 89,
                    startTime: '2024-01-01',
                    endTime: '2024-12-31',
                    status: 1
                },
                {
                    id: 3,
                    name: '包邮券',
                    code: 'FREESHIP',
                    type: 'SHIPPING',
                    value: 0.00,
                    minAmount: 50.00,
                    maxAmount: null,
                    totalCount: 2000,
                    usedCount: 567,
                    startTime: '2024-01-01',
                    endTime: '2024-12-31',
                    status: 1
                },
                {
                    id: 4,
                    name: '618大促',
                    code: 'SALE618',
                    type: 'CASH',
                    value: 100.00,
                    minAmount: 500.00,
                    maxAmount: null,
                    totalCount: 300,
                    usedCount: 298,
                    startTime: '2024-06-01',
                    endTime: '2024-06-18',
                    status: 2
                }
            ];
        }
    </script>
</body>
</html>
