#!/bin/bash

echo "正在启动商品管理系统..."
echo

echo "检查Java环境..."
java -version
if [ $? -ne 0 ]; then
    echo "错误：未找到Java环境，请确保已安装JDK 8或更高版本"
    exit 1
fi

echo
echo "检查Maven环境..."
mvn -version
if [ $? -ne 0 ]; then
    echo "错误：未找到Maven环境，请确保已安装Maven"
    exit 1
fi

echo
echo "编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "编译失败，请检查代码"
    exit 1
fi

echo
echo "启动应用..."
echo "请确保MySQL数据库已启动并执行了初始化脚本"
echo "应用启动后请访问: http://localhost:8080"
echo
mvn spring-boot:run
