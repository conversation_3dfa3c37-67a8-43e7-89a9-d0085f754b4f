<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 商品管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .status-badge {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📦 商品管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 添加商品
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportProducts()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button type="button" class="btn btn-info" onclick="showImportModal()">
                                <i class="fas fa-upload"></i> 导入
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索商品名称或编码">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">全部分类</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">上架</option>
                                    <option value="0">下架</option>
                                    <option value="2">草稿</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary" onclick="searchProducts()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">商品列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>商品图片</th>
                                        <th>商品信息</th>
                                        <th>分类</th>
                                        <th>价格</th>
                                        <th>库存</th>
                                        <th>销量</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="productTableBody">
                                    <!-- 商品数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="商品分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加/编辑商品模态框 -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalTitle">添加商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <input type="hidden" id="productId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productName" class="form-label">商品名称 *</label>
                                    <input type="text" class="form-control" id="productName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productCode" class="form-label">商品编码</label>
                                    <input type="text" class="form-control" id="productCode" placeholder="留空自动生成">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productCategory" class="form-label">商品分类 *</label>
                                    <select class="form-select" id="productCategory" required>
                                        <option value="">请选择分类</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productBrand" class="form-label">品牌</label>
                                    <input type="text" class="form-control" id="productBrand">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="originalPrice" class="form-label">原价</label>
                                    <input type="number" class="form-control" id="originalPrice" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="currentPrice" class="form-label">现价 *</label>
                                    <input type="number" class="form-control" id="currentPrice" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="costPrice" class="form-label">成本价</label>
                                    <input type="number" class="form-control" id="costPrice" step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productStock" class="form-label">库存数量</label>
                                    <input type="number" class="form-control" id="productStock" min="0" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="warningStock" class="form-label">预警库存</label>
                                    <input type="number" class="form-control" id="warningStock" min="0" value="10">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="productDescription" class="form-label">商品描述</label>
                            <textarea class="form-control" id="productDescription" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isRecommend">
                                    <label class="form-check-label" for="isRecommend">推荐商品</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isNew">
                                    <label class="form-check-label" for="isNew">新品</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isHot">
                                    <label class="form-check-label" for="isHot">热销</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            loadCategories();
        });

        // 加载商品列表
        function loadProducts() {
            fetch('/product/list')
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        displayProducts(result.data);
                    } else {
                        alert('加载商品列表失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('加载商品列表失败');
                });
        }

        // 显示商品列表
        function displayProducts(products) {
            const tbody = document.getElementById('productTableBody');
            tbody.innerHTML = '';

            products.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <img src="${product.mainImage || '/images/no-image.png'}" 
                             alt="${product.name}" class="product-image">
                    </td>
                    <td>
                        <div>
                            <strong>${product.name}</strong><br>
                            <small class="text-muted">编码: ${product.code}</small><br>
                            <small class="text-muted">品牌: ${product.brand || '无'}</small>
                        </div>
                    </td>
                    <td>${product.categoryName}</td>
                    <td>
                        <div>
                            <span class="text-danger">¥${product.currentPrice}</span><br>
                            ${product.originalPrice ? `<small class="text-muted text-decoration-line-through">¥${product.originalPrice}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="${product.stock <= product.warningStock ? 'text-danger' : ''}">${product.stock}</span>
                    </td>
                    <td>${product.sales}</td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(product.status)} status-badge">
                            ${getStatusText(product.status)}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取状态徽章样式
        function getStatusBadgeClass(status) {
            switch(status) {
                case 1: return 'bg-success';
                case 0: return 'bg-secondary';
                case 2: return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 1: return '上架';
                case 0: return '下架';
                case 2: return '草稿';
                default: return '未知';
            }
        }

        // 加载分类列表
        function loadCategories() {
            fetch('/category/list')
                .then(response => response.json())
                .then(result => {
                    if (result.data) {
                        const categoryFilter = document.getElementById('categoryFilter');
                        const productCategory = document.getElementById('productCategory');
                        
                        result.data.forEach(category => {
                            const option1 = new Option(category.name, category.id);
                            const option2 = new Option(category.name, category.id);
                            categoryFilter.appendChild(option1);
                            productCategory.appendChild(option2);
                        });
                    }
                })
                .catch(error => console.error('Error loading categories:', error));
        }

        // 显示添加模态框
        function showAddModal() {
            document.getElementById('productModalTitle').textContent = '添加商品';
            document.getElementById('productForm').reset();
            document.getElementById('productId').value = '';
            new bootstrap.Modal(document.getElementById('productModal')).show();
        }

        // 编辑商品
        function editProduct(id) {
            fetch(`/product/${id}`)
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        const product = result.data;
                        document.getElementById('productModalTitle').textContent = '编辑商品';
                        document.getElementById('productId').value = product.id;
                        document.getElementById('productName').value = product.name;
                        document.getElementById('productCode').value = product.code;
                        document.getElementById('productCategory').value = product.categoryId;
                        document.getElementById('productBrand').value = product.brand || '';
                        document.getElementById('originalPrice').value = product.originalPrice || '';
                        document.getElementById('currentPrice').value = product.currentPrice;
                        document.getElementById('costPrice').value = product.costPrice || '';
                        document.getElementById('productStock').value = product.stock;
                        document.getElementById('warningStock').value = product.warningStock;
                        document.getElementById('productDescription').value = product.description || '';
                        document.getElementById('isRecommend').checked = product.isRecommend === 1;
                        document.getElementById('isNew').checked = product.isNew === 1;
                        document.getElementById('isHot').checked = product.isHot === 1;
                        
                        new bootstrap.Modal(document.getElementById('productModal')).show();
                    } else {
                        alert('获取商品信息失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取商品信息失败');
                });
        }

        // 保存商品
        function saveProduct() {
            const form = document.getElementById('productForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const productData = {
                id: document.getElementById('productId').value || null,
                name: document.getElementById('productName').value,
                code: document.getElementById('productCode').value,
                categoryId: document.getElementById('productCategory').value,
                brand: document.getElementById('productBrand').value,
                originalPrice: document.getElementById('originalPrice').value || null,
                currentPrice: document.getElementById('currentPrice').value,
                costPrice: document.getElementById('costPrice').value || null,
                stock: document.getElementById('productStock').value,
                warningStock: document.getElementById('warningStock').value,
                description: document.getElementById('productDescription').value,
                isRecommend: document.getElementById('isRecommend').checked ? 1 : 0,
                isNew: document.getElementById('isNew').checked ? 1 : 0,
                isHot: document.getElementById('isHot').checked ? 1 : 0,
                status: 1
            };

            const url = productData.id ? '/product/update' : '/product/add';
            const method = productData.id ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(productData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 200) {
                    alert(result.message);
                    bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
                    loadProducts();
                } else {
                    alert('保存失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败');
            });
        }

        // 删除商品
        function deleteProduct(id) {
            if (confirm('确定要删除这个商品吗？')) {
                fetch(`/product/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        alert(result.message);
                        loadProducts();
                    } else {
                        alert('删除失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }

        // 搜索商品
        function searchProducts() {
            const keyword = document.getElementById('searchKeyword').value;
            if (keyword.trim()) {
                fetch(`/product/search?keyword=${encodeURIComponent(keyword)}`)
                    .then(response => response.json())
                    .then(result => {
                        if (result.code === 200) {
                            displayProducts(result.data);
                        } else {
                            alert('搜索失败: ' + result.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('搜索失败');
                    });
            } else {
                loadProducts();
            }
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            loadProducts();
        }

        // 导出商品
        function exportProducts() {
            window.location.href = '/product/export';
        }

        // 显示导入模态框
        function showImportModal() {
            alert('导入功能开发中...');
        }
    </script>
</body>
</html>
