package com.example.controller;

import com.example.common.Result;
import com.example.entity.Order;
import com.example.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/order")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * 获取所有订单
     */
    @GetMapping("/list")
    public Result<List<Order>> getAllOrders() {
        return orderService.getAllOrders();
    }
    
    /**
     * 根据ID获取订单
     */
    @GetMapping("/{id}")
    public Result<Order> getOrderById(@PathVariable Long id) {
        return orderService.getOrderById(id);
    }
    
    /**
     * 添加订单
     */
    @PostMapping("/add")
    public Result<String> addOrder(@Valid @RequestBody Order order) {
        return orderService.addOrder(order);
    }
    
    /**
     * 更新订单
     */
    @PutMapping("/update")
    public Result<String> updateOrder(@Valid @RequestBody Order order) {
        return orderService.updateOrder(order);
    }
    
    /**
     * 删除订单
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteOrder(@PathVariable Long id) {
        return orderService.deleteOrder(id);
    }
    
    /**
     * 更新订单状态
     */
    @PutMapping("/{id}/status")
    public Result<String> updateOrderStatus(@PathVariable Long id, @RequestParam Integer status) {
        return orderService.updateOrderStatus(id, status);
    }
    
    /**
     * 根据用户ID获取订单
     */
    @GetMapping("/user/{userId}")
    public Result<List<Order>> getOrdersByUserId(@PathVariable Long userId) {
        return orderService.getOrdersByUserId(userId);
    }
    
    /**
     * 根据状态获取订单
     */
    @GetMapping("/status/{status}")
    public Result<List<Order>> getOrdersByStatus(@PathVariable Integer status) {
        return orderService.getOrdersByStatus(status);
    }
}
