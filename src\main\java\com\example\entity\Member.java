package com.example.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员实体类
 */
@Data
public class Member {
    
    /**
     * 会员ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 会员编号
     */
    private String memberNo;
    
    /**
     * 会员等级 (BRONZE-青铜, SILVER-白银, GOLD-黄金, PLATINUM-铂金, DIAMOND-钻石)
     */
    private String level;
    
    /**
     * 积分
     */
    private Integer points;
    
    /**
     * 累计消费金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 累计订单数
     */
    private Integer totalOrders;
    
    /**
     * 会员到期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 状态 (0-禁用, 1-正常)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
