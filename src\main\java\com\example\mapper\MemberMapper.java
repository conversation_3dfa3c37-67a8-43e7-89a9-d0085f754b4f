package com.example.mapper;

import com.example.entity.Member;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员数据访问层
 */
@Mapper
public interface MemberMapper {
    
    /**
     * 查询所有会员
     */
    List<Member> selectAll();
    
    /**
     * 根据ID查询会员
     */
    Member selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询会员
     */
    Member selectByUserId(@Param("userId") Long userId);
    
    /**
     * 插入会员
     */
    int insert(Member member);
    
    /**
     * 更新会员
     */
    int update(Member member);
    
    /**
     * 删除会员
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 更新会员积分
     */
    int updatePoints(@Param("id") Long id, @Param("points") Integer points);
    
    /**
     * 更新会员等级
     */
    int updateLevel(@Param("id") Long id, @Param("level") String level);
    
    /**
     * 根据等级查询会员
     */
    List<Member> selectByLevel(@Param("level") String level);
    
    /**
     * 统计会员总数
     */
    int countTotal();
    
    /**
     * 统计各等级会员数量
     */
    List<Member> countByLevel();
}
