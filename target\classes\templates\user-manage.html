<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选 - 用户管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        .status-badge {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="brand-title">🛍️ 尚品甄选</h4>
                        <small class="text-white-50">Premium E-commerce</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/user/manage">
                                <i class="fas fa-users"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/category">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/product/manage">
                                <i class="fas fa-box"></i> 商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/order/manage">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/member/manage">
                                <i class="fas fa-crown"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/coupon/manage">
                                <i class="fas fa-ticket-alt"></i> 优惠券管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">👥 用户管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 添加用户
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportUsers()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索用户名或邮箱">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="roleFilter">
                                    <option value="">全部角色</option>
                                    <option value="ADMIN">管理员</option>
                                    <option value="USER">普通用户</option>
                                    <option value="VIP">VIP用户</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary" onclick="searchUsers()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">用户列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>头像</th>
                                        <th>用户信息</th>
                                        <th>联系方式</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="userTableBody">
                                    <!-- 用户数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="用户分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加/编辑用户模态框 -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">添加用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名 *</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">密码 *</label>
                                    <input type="password" class="form-control" id="password" required>
                                    <small class="text-muted">编辑时留空表示不修改密码</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">手机号</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="realName" class="form-label">真实姓名</label>
                                    <input type="text" class="form-control" id="realName">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">性别</label>
                                    <select class="form-select" id="gender">
                                        <option value="2">未知</option>
                                        <option value="1">男</option>
                                        <option value="0">女</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">角色 *</label>
                                    <select class="form-select" id="role" required>
                                        <option value="USER">普通用户</option>
                                        <option value="VIP">VIP用户</option>
                                        <option value="ADMIN">管理员</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">状态 *</label>
                                    <select class="form-select" id="status" required>
                                        <option value="1">正常</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="remark" class="form-label">备注</label>
                            <textarea class="form-control" id="remark" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        // 加载用户列表
        function loadUsers() {
            fetch('/user/list')
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        displayUsers(result.data);
                    } else {
                        alert('加载用户列表失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('加载用户列表失败');
                });
        }

        // 显示用户列表
        function displayUsers(users) {
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <img src="${user.avatar || '/images/default-avatar.png'}" 
                             alt="${user.username}" class="user-avatar">
                    </td>
                    <td>
                        <div>
                            <strong>${user.username}</strong><br>
                            <small class="text-muted">${user.realName || '未设置'}</small>
                        </div>
                    </td>
                    <td>
                        <div>
                            ${user.email || '未设置'}<br>
                            <small class="text-muted">${user.phone || '未设置'}</small>
                        </div>
                    </td>
                    <td>
                        <span class="badge ${getRoleBadgeClass(user.role)} status-badge">
                            ${getRoleText(user.role)}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(user.status)} status-badge">
                            ${getStatusText(user.status)}
                        </span>
                    </td>
                    <td>
                        <small>${formatDateTime(user.createTime)}</small>
                    </td>
                    <td>
                        <small>${user.lastLoginTime ? formatDateTime(user.lastLoginTime) : '从未登录'}</small>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" 
                                ${user.role === 'ADMIN' ? 'disabled' : ''}>
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取角色徽章样式
        function getRoleBadgeClass(role) {
            switch(role) {
                case 'ADMIN': return 'bg-danger';
                case 'VIP': return 'bg-warning';
                case 'USER': return 'bg-primary';
                default: return 'bg-secondary';
            }
        }

        // 获取角色文本
        function getRoleText(role) {
            switch(role) {
                case 'ADMIN': return '管理员';
                case 'VIP': return 'VIP用户';
                case 'USER': return '普通用户';
                default: return '未知';
            }
        }

        // 获取状态徽章样式
        function getStatusBadgeClass(status) {
            return status === 1 ? 'bg-success' : 'bg-secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            return status === 1 ? '正常' : '禁用';
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 显示添加模态框
        function showAddModal() {
            document.getElementById('userModalTitle').textContent = '添加用户';
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('password').required = true;
            new bootstrap.Modal(document.getElementById('userModal')).show();
        }

        // 编辑用户
        function editUser(id) {
            fetch(`/user/${id}`)
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        const user = result.data;
                        document.getElementById('userModalTitle').textContent = '编辑用户';
                        document.getElementById('userId').value = user.id;
                        document.getElementById('username').value = user.username;
                        document.getElementById('password').value = '';
                        document.getElementById('password').required = false;
                        document.getElementById('email').value = user.email || '';
                        document.getElementById('phone').value = user.phone || '';
                        document.getElementById('realName').value = user.realName || '';
                        document.getElementById('gender').value = user.gender || 2;
                        document.getElementById('role').value = user.role;
                        document.getElementById('status').value = user.status;
                        document.getElementById('remark').value = user.remark || '';
                        
                        new bootstrap.Modal(document.getElementById('userModal')).show();
                    } else {
                        alert('获取用户信息失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取用户信息失败');
                });
        }

        // 保存用户
        function saveUser() {
            const form = document.getElementById('userForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const userData = {
                id: document.getElementById('userId').value || null,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                realName: document.getElementById('realName').value,
                gender: parseInt(document.getElementById('gender').value),
                role: document.getElementById('role').value,
                status: parseInt(document.getElementById('status').value),
                remark: document.getElementById('remark').value
            };

            const url = userData.id ? '/user/update' : '/user/add';
            const method = userData.id ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 200) {
                    alert(result.message);
                    bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
                    loadUsers();
                } else {
                    alert('保存失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败');
            });
        }

        // 删除用户
        function deleteUser(id) {
            if (confirm('确定要删除这个用户吗？')) {
                fetch(`/user/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        alert(result.message);
                        loadUsers();
                    } else {
                        alert('删除失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }

        // 搜索用户
        function searchUsers() {
            // 这里可以实现搜索逻辑
            alert('搜索功能开发中...');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('roleFilter').value = '';
            document.getElementById('statusFilter').value = '';
            loadUsers();
        }

        // 导出用户
        function exportUsers() {
            alert('导出功能开发中...');
        }
    </script>
</body>
</html>
